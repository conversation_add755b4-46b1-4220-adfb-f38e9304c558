import { createClient } from '@/utils/supabase/server';
import CheckoutPage from './_components/Checkout'; // The client wizard

/**
 * This is the main server-side entry point for the /checkout route.
 * Its job is to fetch all necessary server-side data and pass it
 * down to the client-side components.
 */
export default async function CheckoutRoutePage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  return (
    <main className="mx-auto min-h-screen">
      {/* Pass the server-fetched user data as a prop */}
      <CheckoutPage user={user} />
    </main>
  );
}
