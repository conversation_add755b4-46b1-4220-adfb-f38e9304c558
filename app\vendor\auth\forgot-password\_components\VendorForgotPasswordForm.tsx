'use client';

import { useActionState, useRef, useEffect } from 'react';
import {
  vendorForgotPassword,
  type VendorForgotPasswordState,
} from '@/app/actions/vendor-auth';
import NextLink from 'next/link';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Link as ChakraLink,
  Alert,
  AlertIcon,
  AlertTitle,
  Spinner,
} from '@chakra-ui/react';

export default function VendorForgotPasswordForm() {
  const formRef = useRef<HTMLFormElement>(null);

  const [formState, formAction, isPending] = useActionState<
    VendorForgotPasswordState,
    FormData
  >(vendorForgotPassword, { error: null, success: false, message: null });

  useEffect(() => {
    if (formState.error) {
      formRef.current?.reset();
    }
  }, [formState]);

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="md"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Reset Password
          </Heading>

          <Text textAlign="center" color="gray.600" fontSize="sm">
            Enter your business email to receive a password reset link
          </Text>

          {formState.error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {formState.error}
            </Alert>
          )}

          {formState.success && formState.message && (
            <Alert status="success" borderRadius="md">
              <AlertIcon />
              <AlertTitle>{formState.message}</AlertTitle>
            </Alert>
          )}

          <form ref={formRef} action={formAction}>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel htmlFor="email" color="gray.600">
                  Business Email
                </FormLabel>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  focusBorderColor="primary"
                  placeholder="<EMAIL>"
                  disabled={isPending}
                />
              </FormControl>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Send Reset Link
              </Button>
            </VStack>
          </form>

          <Text textAlign="center">
            <ChakraLink
              as={NextLink}
              href="/vendor/auth/login"
              color="blue.600"
              fontWeight="medium"
              _hover={{ color: 'blue.500' }}
            >
              Back to Sign In
            </ChakraLink>
          </Text>
        </VStack>
      </Box>
    </Flex>
  );
}
