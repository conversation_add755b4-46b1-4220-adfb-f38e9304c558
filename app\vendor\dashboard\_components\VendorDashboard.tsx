'use client';

import {
  Box,
  Flex,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
} from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { Database } from '@/types/supabase';

type Vendor = Database['public']['Tables']['vendor']['Row'];

interface VendorDashboardProps {
  vendor: Vendor;
}

export default function VendorDashboard({ vendor }: VendorDashboardProps) {
  const router = useRouter();

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'approved':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'rejected':
        return 'red';
      case 'suspended':
        return 'orange';
      case null:
        return 'gray';
      default:
        return 'gray';
    }
  };

  const getStatusText = (status: string | null) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending Approval';
      case 'rejected':
        return 'Rejected';
      case 'suspended':
        return 'Suspended';
      case null:
        return 'Not Set';
      default:
        return 'Unknown';
    }
  };

  if (vendor.approval_status === 'pending') {
    return (
      <Flex minH="100vh" align="center" justify="center" bg="gray.50" px={4}>
        <Box
          maxW="md"
          w="full"
          bg="white"
          borderRadius="2xl"
          boxShadow="lg"
          p={8}
        >
          <VStack spacing={6} align="stretch">
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Account Pending Approval</AlertTitle>
                <AlertDescription>
                  Your vendor account is currently under review. We&apos;ll
                  notify you once it&apos;s approved.
                </AlertDescription>
              </Box>
            </Alert>

            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Company Name
                </Text>
                <Text fontWeight="medium">{vendor.name}</Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Status
                </Text>
                <Badge colorScheme={getStatusColor(vendor.approval_status)}>
                  {getStatusText(vendor.approval_status)}
                </Badge>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Submitted On
                </Text>
                <Text>
                  {vendor.created_at
                    ? new Date(vendor.created_at).toLocaleDateString()
                    : 'Not available'}
                </Text>
              </Box>
            </VStack>

            <Divider />

            <VStack spacing={3}>
              <Text fontSize="sm" color="gray.600" textAlign="center">
                What happens next?
              </Text>
              <VStack spacing={2} align="start" fontSize="sm" color="gray.600">
                <Text>• Our team will review your application</Text>
                <Text>• We&apos;ll verify your business documents</Text>
                <Text>• You&apos;ll receive an email notification</Text>
                <Text>• Once approved, you can start selling</Text>
              </VStack>
            </VStack>

            <Button
              onClick={() => router.push('/vendor/auth/company-info')}
              variant="outline"
              size="sm"
            >
              Update Company Information
            </Button>
          </VStack>
        </Box>
      </Flex>
    );
  }

  if (vendor.approval_status === 'rejected') {
    return (
      <Flex minH="100vh" align="center" justify="center" bg="gray.50" px={4}>
        <Box
          maxW="md"
          w="full"
          bg="white"
          borderRadius="2xl"
          boxShadow="lg"
          p={8}
        >
          <VStack spacing={6} align="stretch">
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Account Rejected</AlertTitle>
                <AlertDescription>
                  Your vendor account application was not approved. Please
                  contact support for more information.
                </AlertDescription>
              </Box>
            </Alert>

            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Company Name
                </Text>
                <Text fontWeight="medium">{vendor.name}</Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={2}>
                  Status
                </Text>
                <Badge colorScheme={getStatusColor(vendor.approval_status)}>
                  {getStatusText(vendor.approval_status)}
                </Badge>
              </Box>
            </VStack>

            <Button
              onClick={() => router.push('/vendor/auth/company-info')}
              variant="outline"
              size="sm"
            >
              Update Information & Reapply
            </Button>
          </VStack>
        </Box>
      </Flex>
    );
  }

  // Approved vendor dashboard
  return (
    <Box minH="100vh" bg="gray.50" p={6}>
      <Box maxW="7xl" mx="auto">
        <VStack spacing={6} align="stretch">
          <Flex justify="space-between" align="center">
            <Box>
              <Heading size="lg" color="gray.800">
                Welcome back, {vendor.name}
              </Heading>
              <Text color="gray.600">
                {vendor.headline || 'Manage your vendor account'}
              </Text>
            </Box>
            <Badge
              colorScheme={getStatusColor(vendor.approval_status)}
              size="lg"
            >
              {getStatusText(vendor.approval_status)}
            </Badge>
          </Flex>

          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
            <Card>
              <CardHeader>
                <Heading size="sm">Total Sales</Heading>
              </CardHeader>
              <CardBody>
                <Stat>
                  <StatNumber>$0.00</StatNumber>
                  <StatLabel>This Month</StatLabel>
                  <StatHelpText>No sales yet</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <Heading size="sm">Active Products</Heading>
              </CardHeader>
              <CardBody>
                <Stat>
                  <StatNumber>0</StatNumber>
                  <StatLabel>Products Listed</StatLabel>
                  <StatHelpText>Start adding products</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <Heading size="sm">Orders</Heading>
              </CardHeader>
              <CardBody>
                <Stat>
                  <StatNumber>0</StatNumber>
                  <StatLabel>Pending Orders</StatLabel>
                  <StatHelpText>No orders yet</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          <Card>
            <CardHeader>
              <Heading size="md">Quick Actions</Heading>
            </CardHeader>
            <CardBody>
              <HStack spacing={4}>
                <Button colorScheme="blue" variant="outline">
                  Add Product
                </Button>
                <Button colorScheme="green" variant="outline">
                  View Orders
                </Button>
                <Button colorScheme="purple" variant="outline">
                  Analytics
                </Button>
                <Button colorScheme="gray" variant="outline">
                  Settings
                </Button>
              </HStack>
            </CardBody>
          </Card>

          <Alert status="info" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle>Getting Started</AlertTitle>
              <AlertDescription>
                Your vendor account is approved! Start by adding your first
                product to begin selling on our marketplace.
              </AlertDescription>
            </Box>
          </Alert>
        </VStack>
      </Box>
    </Box>
  );
}
