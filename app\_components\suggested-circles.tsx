'use client';
import { CSSProperties, useState } from 'react';
import { Box, Flex, Heading, Spinner } from '@chakra-ui/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import { FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { CircleCard } from '@/components/cards';
import { Circle } from '@/utils/supabase/queries';

interface SuggestedCirclesProps {
  circles: Circle[];
}

const SuggestedCircles = ({ circles }: SuggestedCirclesProps) => {
  const [isSwiperReady, setIsSwiperReady] = useState(false);

  if (!circles || circles.length === 0) {
    return null;
  }

  return (
    <Box
      as="section"
      shadow="md"
      borderRadius="2xl"
      bg="white"
      maxW="container.xl"
      mx="auto"
      py={8}
      px={{ base: 4, md: 8 }}
      my={10}
    >
      <Heading
        as="h2"
        fontSize={{ base: 'xl', md: '2xl' }}
        fontWeight="bold"
        mb={6}
      >
        Circles you might like
      </Heading>

      <Box position="relative" minH="full">
        {!isSwiperReady && (
          <Flex
            align="center"
            justify="center"
            position="absolute"
            top="0"
            left="0"
            w="full"
            h="full"
            zIndex={10}
          >
            <Spinner
              size="xl"
              thickness="4px"
              speed="0.65s"
              emptyColor="gray.200"
              color="primary"
            />
          </Flex>
        )}

        <Box
          opacity={isSwiperReady ? 1 : 0}
          transition="opacity 0.4s ease-in-out"
        >
          <Swiper
            onSwiper={() => setIsSwiperReady(true)}
            modules={[Navigation]}
            spaceBetween={16}
            slidesPerView={'auto'}
            breakpoints={{
              320: { slidesPerView: 1.15, spaceBetween: 12 },
              640: { slidesPerView: 2.2, spaceBetween: 16 },
              768: { slidesPerView: 2.5, spaceBetween: 16 },
              1024: { slidesPerView: 3.5, spaceBetween: 20 },
              1280: { slidesPerView: 4, spaceBetween: 20 },
            }}
            navigation={{
              nextEl: '.swiper-button-next-suggested',
              prevEl: '.swiper-button-prev-suggested',
            }}
            style={{ '--swiper-navigation-size': '24px' } as CSSProperties}
          >
            {circles.map(circle => (
              <SwiperSlide key={circle.id} style={{ height: 'auto' }}>
                <CircleCard circle={circle} />
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>

        {/* Navigation buttons outside the opacity box */}
        <Flex
          className="swiper-button-prev-suggested"
          align="center"
          justify="center"
          position="absolute"
          left={{ base: -2, md: '-2rem' }}
          top="50%"
          zIndex={10}
          transform="translateY(-50%)"
          display={{ base: 'none', md: 'flex' }}
          w="44px"
          h="44px"
          borderRadius="full"
          bg="white"
          cursor="pointer"
          boxShadow="md"
          border="1px"
          borderColor="gray.200"
          _hover={{ bg: 'gray.50' }}
        >
          <FiChevronLeft size={24} />
        </Flex>
        <Flex
          className="swiper-button-next-suggested"
          align="center"
          justify="center"
          position="absolute"
          right={{ base: -2, md: '-2rem' }}
          top="50%"
          zIndex={10}
          transform="translateY(-50%)"
          display={{ base: 'none', md: 'flex' }}
          w="44px"
          h="44px"
          borderRadius="full"
          bg="white"
          cursor="pointer"
          boxShadow="md"
          border="1px"
          borderColor="gray.200"
          _hover={{ bg: 'gray.50' }}
        >
          <FiChevronRight size={24} />
        </Flex>
      </Box>
    </Box>
  );
};

export default SuggestedCircles;
