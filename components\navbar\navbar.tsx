'use client';

import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  IconButton,
  Divider,
  useDisclosure,
  Center,
} from '@chakra-ui/react';
import { HiMenu } from 'react-icons/hi';
import { useRouter } from 'next/navigation';
import CategoriesSubnav from './categories-subnavigation';
import MobileDrawer from './mobile-drawer';
import SearchBox from './search-box';
import NextLink from 'next/link';
import { Link } from '@chakra-ui/next-js';
import { usePathname, useSearchParams } from 'next/navigation';
import { Category } from '@/utils/supabase/queries';
import { useCategories } from '@/providers/CategoriesContext';

interface CollabNavbarProps {
  authButton: React.ReactNode;
}

const CollabNavbar: React.FC<CollabNavbarProps> = ({
  authButton, // Destructure the new prop
}) => {
  const router = useRouter();
  const { categories } = useCategories();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [drawerView, setDrawerView] = useState<'main' | 'sub' | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const pathname = usePathname();
  const searchParams = useSearchParams();

  if (pathname.startsWith('/checkout') && searchParams.get('step') === 'auth') {
    return null;
  }

  const handleSubcategoryClick = (
    categoryId: string,
    subcategoryId: string
  ) => {
    router.push(`/category/${categoryId}/${subcategoryId}`);
    onClose();
  };

  const handleOpenDrawer = () => {
    setDrawerView('main');
    onOpen();
  };

  const openSubcategory = (category: Category) => {
    setSelectedCategory(category);
    setDrawerView('sub');
  };

  const goBackToMain = () => {
    setDrawerView('main');
    setSelectedCategory(null);
  };

  return (
    <Box as="nav" bg="primary" position="sticky" top="0" zIndex="sticky">
      <Flex
        py={3}
        align="center"
        justify="space-between"
        bg="primary"
        mx="auto"
        maxW="container.xl"
      >
        <Flex align="center">
          <IconButton
            aria-label="Open menu"
            icon={<HiMenu />}
            variant="ghost"
            onClick={handleOpenDrawer}
            mr={2}
            fontSize="xl"
            display={{ base: 'inline-flex', md: 'none' }}
          />
          <Link as={NextLink} href="/" _hover={{ textDecoration: 'none' }}>
            <Text fontSize={{ base: 'xl', md: '2xl' }} fontWeight="bold">
              Collab
            </Text>
          </Link>
        </Flex>

        <Box
          flex="1"
          maxW={{ md: '100%', lg: '50%' }}
          mx={6}
          display={{ base: 'none', md: 'block' }}
        >
          <SearchBox />
        </Box>

        <Flex align="center" gap={1}>
          <Link
            as={NextLink}
            href="/"
            _hover={{ textDecoration: 'none' }}
            display={{ base: 'none', md: 'flex' }}
          >
            <Text fontSize={{ base: 'sm', md: 'md' }} fontWeight="semibold">
              Home
            </Text>
          </Link>
          <Center height="25" px="10px" display={{ base: 'none', md: 'flex' }}>
            <Divider
              orientation="vertical"
              borderColor="black"
              borderWidth="1px"
            />
          </Center>

          <Link as={NextLink} href="/ar" _hover={{ textDecoration: 'none' }}>
            <Text
              fontSize={{ base: 'sm', md: 'md' }}
              fontWeight="semibold"
              mx={{ base: '4', md: 'auto' }}
            >
              العربية
            </Text>
          </Link>
          <Center height="25" px="10px" display={{ base: 'none', md: 'flex' }}>
            <Divider
              orientation="vertical"
              borderColor="black"
              borderWidth="1px"
            />
          </Center>

          {/* --- ARCHITECTURAL CHANGE: Render the passed-in server component --- */}
          <Box display={{ base: 'none', md: 'flex' }}>{authButton}</Box>
        </Flex>
      </Flex>

      <Box mx={3} pb={3} bg="primary" display={{ base: 'block', md: 'none' }}>
        <SearchBox />
      </Box>

      <CategoriesSubnav categories={categories} />

      <MobileDrawer
        isOpen={isOpen}
        onClose={onClose}
        drawerView={drawerView}
        selectedCategory={selectedCategory}
        categories={categories}
        openSubcategory={openSubcategory}
        goBackToMain={goBackToMain}
        handleSubcategoryClick={handleSubcategoryClick}
      />
    </Box>
  );
};

export default CollabNavbar;
