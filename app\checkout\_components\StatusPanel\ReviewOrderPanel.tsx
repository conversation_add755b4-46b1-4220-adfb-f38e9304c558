'use client';
import { Heading, VStack } from '@chakra-ui/react';
import type { CartItem } from '@/providers/CartContext';
import { OrderItemCard } from './OrderItemCard';

type ReviewOrderPanelProps = {
  item: CartItem;
  onRemove: () => void;
};

export const ReviewOrderPanel = ({ item, onRemove }: ReviewOrderPanelProps) => {
  return (
    <VStack align="stretch" spacing={4}>
      <Heading as="h3" size="md">
        Review Order
      </Heading>
      <OrderItemCard item={item} onRemove={onRemove} />
    </VStack>
  );
};
