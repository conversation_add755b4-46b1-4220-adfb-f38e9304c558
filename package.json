{"name": "collab-customer-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build-dev": "__NEXT_TEST_MODE=true next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next node_modules .turbo dist coverage .vercel", "test": "vitest run --coverage", "format": "prettier --write .", "format:check": "prettier --fix --check . ", "db:login": "supabase login", "db:link": "supabase link --project-ref gwrdqokfydjzqjsdzytr", "db:create": "supabase migration new", "db:migrate": "supabase db push", "db:reset": "supabase db reset", "db:types": "supabase gen types typescript --project-id gwrdqokfydjzqjsdzytr --schema public > types/supabase.ts", "db:local:start": "supabase start", "db:local:stop": "supabase stop", "db:local:restart": "supabase restart", "db:local:reset": "supabase db reset --local", "db:local:migrate": "supabase db push --local", "db:local:types": "supabase gen types typescript --local --schema public > types/supabase.ts", "db:local:studio": "supabase studio", "dev:local": "npm run db:local:start && npm run dev"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/next-js": "^2.4.2", "@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "dotenv": "^17.2.3", "framer-motion": "^12.11.0", "mailersend": "^2.6.0", "next": "^15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "swiper": "^11.2.8", "tailwind-merge": "^3.0.2", "twilio": "^5.8.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9", "eslint-config-next": "15.1.6", "jsdom": "^26.1.0", "postcss": "^8", "prettier": "^3.6.1", "supabase": "^2.40.7", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}