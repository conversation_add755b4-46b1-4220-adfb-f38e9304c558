import React from 'react';
import {
  <PERSON>lex,
  Text,
  Button,
  Badge,
  Image,
  HStack,
  VStack,
  Icon,
  Box,
  Link as ChakraLink,
} from '@chakra-ui/react';
import { FaClock } from 'react-icons/fa';
import SemiCircleCountDown from './semi-circle-countdown';
import Link from 'next/link';
import { Circle } from '@/utils/supabase/queries';
import { getPrice } from '@/utils/lib';
type CustomBadgeProps = {
  children: React.ReactNode;
  colorScheme?: string;
};

export const CustomBadge = ({
  children,
  colorScheme = 'orange',
  ...rest
}: CustomBadgeProps) => (
  <Badge
    variant="solid"
    colorScheme={colorScheme}
    px={3}
    py={2}
    borderRadius="full"
    textTransform="none"
    fontSize="sm"
    lineHeight="1"
    maxW="fit-content"
    {...rest}
  >
    {children}
  </Badge>
);

const CircleCard = ({ circle }: { circle: Circle }) => {
  if (!circle) {
    return null;
  }

  const productName = circle.product?.name;
  const imageUrl = circle.product.images[0] ?? '/default-product.png';
  const basePrice = circle.sale_price ?? 0;
  const totalJoiners = circle.current_participants ?? 0;
  const maxJoiners = circle.max_participants ?? 1;
  const marketPrice = circle.product?.market_price ?? null;
  const endDateStr = circle.end_date ?? new Date().toISOString();

  // Calculate current price based on tiers and participants
  const pricingTiers = circle.pricing_tiers || [];
  const { price, nextTier, tierChangeAt } = getPrice(
    pricingTiers,
    basePrice,
    totalJoiners
  );
  // Status and timer logic
  const isFull = totalJoiners >= maxJoiners;
  const isEnded = new Date(endDateStr) <= new Date();

  const getTimerText = () => {
    if (isEnded) return 'Ended';
    const diffTime = new Date(endDateStr).getTime() - new Date().getTime();
    if (diffTime <= 0) return 'Ended';
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days`;
  };
  const timer = getTimerText();

  const badgeText = isFull ? 'Circle Full' : 'Sale Circle';
  const badgeColorScheme = isFull ? 'gray' : 'orange';
  const isButtonDisabled = isFull || isEnded;

  return (
    <Flex
      direction="column"
      bg="white"
      borderRadius="2xl"
      border="1px"
      borderColor="gray.200"
      boxShadow="sm"
      p={4}
      h="full"
      minH="10rem"
      position="relative"
    >
      <Flex position="absolute" top={2} left={4} zIndex={10}>
        {isFull ? (
          <Badge
            variant="solid"
            colorScheme={badgeColorScheme}
            px={3}
            py={1}
            borderRadius="full"
            textTransform="none"
          >
            {badgeText}
          </Badge>
        ) : (
          <CustomBadge colorScheme={badgeColorScheme}>{badgeText}</CustomBadge>
        )}
      </Flex>

      <VStack spacing={2} align="stretch" flex="1">
        <Link href={`/circle/${circle.id}`}>
          <Flex justifyContent="center" alignItems="center" h="140px" mt={2}>
            <Image
              src={imageUrl}
              alt={productName ?? 'Product Image'}
              objectFit="contain"
              maxH="140px"
              maxW="100%"
            />
          </Flex>
        </Link>
        <Box className="swiper-no-swiping">
          <Text
            fontWeight="semibold"
            color="gray.800"
            noOfLines={2}
            minH="40px"
          >
            {productName}
          </Text>
        </Box>
        <HStack justify="space-between" align="center">
          <Text fontSize="2xl" fontWeight="bold" color="gray.900">
            {`﷼${price?.toLocaleString()}`}
          </Text>
          {!isButtonDisabled && (
            <HStack
              bg="gray.800"
              color="white"
              px={1}
              py={1}
              borderRadius="full"
              spacing={2}
            >
              <Icon as={FaClock} />
              <Text fontSize="xs" fontWeight="bold">
                {timer}
              </Text>
            </HStack>
          )}
        </HStack>
        <VStack align="stretch" spacing={1} minH="40px">
          {nextTier && (
            <HStack justify="start" fontSize="xs" color="gray.600">
              <Text>
                Next:{' '}
                <Text as="span" fontWeight="medium" color="gray.800">
                  ${nextTier.price.toLocaleString()}
                </Text>
              </Text>
              <Badge colorScheme="green" variant="outline" fontSize="0.6rem">
                at {tierChangeAt} joiners
              </Badge>
            </HStack>
          )}
          {marketPrice && (
            <Text fontSize="xs" color="gray.600" align="left">
              Market:{' '}
              <Text
                as={isButtonDisabled ? 's' : 'span'}
                fontWeight="medium"
                color="gray.800"
              >
                ${marketPrice.toLocaleString()}
              </Text>
            </Text>
          )}
        </VStack>

        <SemiCircleCountDown
          value={totalJoiners}
          max={maxJoiners}
          pricingTiers={pricingTiers}
        />
      </VStack>

      <Button
        bg={isButtonDisabled ? 'gray.400' : '#AFFF02'}
        color={isButtonDisabled ? 'white' : 'black'}
        size="sm"
        fontSize="md"
        fontWeight="bold"
        w="full"
        isDisabled={isButtonDisabled}
        _disabled={{
          bg: 'gray.400',
          opacity: 1,
          cursor: 'not-allowed',
        }}
        _hover={{
          bg: isButtonDisabled ? undefined : '#99e602',
          textDecoration: 'none',
        }}
        mt={3}
        as={ChakraLink}
        href={`/circle/${circle.id}`}
      >
        {isFull
          ? 'Circle Full'
          : isEnded
            ? 'Circle Ended'
            : `Join for ﷼ ${price?.toLocaleString()}`}
      </Button>
    </Flex>
  );
};

export default CircleCard;
