import { NextResponse } from 'next/server';
import twilio from 'twilio';

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);
const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID as string;

export async function POST(request: Request) {
  const { phone } = await request.json();

  if (!phone) {
    return NextResponse.json(
      { error: 'Phone number is required.' },
      { status: 400 }
    );
  }

  try {
    const verification = await twilioClient.verify.v2
      .services(verifyServiceSid)
      .verifications.create({ to: phone, channel: 'sms' });

    if (verification.status === 'pending') {
      return NextResponse.json({ success: true });
    } else {
      throw new Error('Verification failed to start.');
    }
  } catch (error) {
    console.error('Twilio verification start error:', error);
    return NextResponse.json(
      { error: 'Failed to send verification code.', details: `${error}` },
      { status: 500 }
    );
  }
}
