import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorDashboard from './_components/VendorDashboard';

export default async function VendorDashboardPage() {
  const supabase = await createClient();

  // Check if user is logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/vendor/auth/login');
  }

  // Get vendor information
  const { data: vendor } = await supabase
    .from('vendor')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (!vendor) {
    redirect('/vendor/auth/signup');
  }

  // Check if company info is complete
  if (!vendor.headline || !vendor.description) {
    redirect('/vendor/auth/company-info');
  }

  return <VendorDashboard vendor={vendor} />;
}
