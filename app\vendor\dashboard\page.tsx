import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorDashboard from './_components/VendorDashboard';

export default async function VendorDashboardPage() {
  const supabase = await createClient();

  // Check if user is logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/vendor/auth/login');
  }

  // Get vendor information
  const { data: vendor } = await supabase
    .from('vendor')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (!vendor) {
    redirect('/vendor/auth/signup');
  }

  // Check if user's email is verified
  if (!user.email_confirmed_at) {
    redirect('/vendor/auth/login');
  }

  // Check if company info is complete - all required fields
  if (!vendor.headline || !vendor.description || !vendor.date_of_incorporation) {
    redirect('/vendor/auth/company-info');
  }

  return <VendorDashboard vendor={vendor} />;
}
