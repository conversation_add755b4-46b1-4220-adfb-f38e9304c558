import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import SignupForm from './_components/signup-form';

export default async function SignupPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect('/');
  }

  return <SignupForm />;
}
