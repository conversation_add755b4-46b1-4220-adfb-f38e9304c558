'use client';
import { useState } from 'react';
import {
  Box,
  Heading,
  VStack,
  Flex,
  Text,
  Icon,
  Progress,
  Input,
  Button,
  HStack,
  useToast,
} from '@chakra-ui/react';
import { AiOutlineCheckCircle, AiOutlineInfoCircle } from 'react-icons/ai';
import { useCart } from '@/providers/CartContext';
import { saveCartAction } from '@/app/actions/cart';

interface CartSummaryCardProps {
  summary: {
    nextTierPrice: number;
    nextTierJoiners: number;
    priceHoldUntil: string;
    progressValue: number;
    subtotal: number;
    shippingFee: number | 'Free';
    total: number;
  };
  handleNext: () => void;
}

export const CartSummaryCard = ({
  summary,
  handleNext,
}: CartSummaryCardProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast({ position: 'top' });
  const { item, itemCount, cartId } = useCart();

  const shippingDisplay =
    summary.shippingFee === 'Free'
      ? 'Free'
      : `SAR ${summary.shippingFee.toFixed(2)}`;

  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsLoading(true);

    if (!item || !cartId || itemCount <= 0) {
      toast({ title: 'Your cart is empty.', status: 'error' });
      setIsLoading(false);
      return;
    }

    const result = await saveCartAction(item, itemCount, cartId);

    if (result.success) {
      toast({
        title: 'Cart saved securely.',
        status: 'success',
        duration: 1500,
      });
      handleNext();
    } else {
      toast({
        title: 'Could not save cart',
        description: result.error || 'An unexpected error occurred.',
        status: 'error',
      });
      setIsLoading(false);
    }
  };

  return (
    <Box
      as="form"
      onSubmit={handleFormSubmit}
      bg="white"
      p={6}
      borderRadius="xl"
    >
      <VStack align="stretch" spacing={4}>
        <Heading as="h2" size="md" fontWeight="bold">
          Cart Summary
        </Heading>
        <VStack align="stretch" spacing={2}>
          <HStack color="green.500">
            <Icon as={AiOutlineCheckCircle} boxSize={5} />
            <Text fontSize="sm">
              Next Tier: SAR {summary.nextTierPrice.toFixed(2)} (in{' '}
              {summary.nextTierJoiners} more joiners)
            </Text>
          </HStack>
          <HStack color="gray.600">
            <Icon as={AiOutlineInfoCircle} boxSize={5} />
            <Text fontSize="sm">Price Hold for: {summary.priceHoldUntil}</Text>
          </HStack>
        </VStack>
        <Progress
          value={summary.progressValue}
          size="sm"
          colorScheme="green"
          borderRadius="full"
        />
        <HStack>
          <Input placeholder="Coupon" bg="white" />
          <Button bg="#AFFF02" color="black" _hover={{ bg: '#99e602' }}>
            Apply
          </Button>
        </HStack>
        <VStack align="stretch" spacing={2} py={2}>
          <Flex justify="space-between">
            <Text color="gray.600">Subtotal</Text>
            <Text fontWeight="medium">SAR {summary.subtotal.toFixed(2)}</Text>
          </Flex>
          <Flex justify="space-between">
            <Text color="gray.600">Shipping Fee</Text>
            <Text fontWeight="medium">{shippingDisplay}</Text>
          </Flex>
        </VStack>
        <Flex
          justify="space-between"
          align="center"
          borderTop="1px solid"
          borderColor="gray.300"
          pt={4}
        >
          <Text fontSize="lg" fontWeight="bold">
            Total
          </Text>
          <Text fontSize="2xl" fontWeight="bold">
            SAR {summary.total.toFixed(2)}
          </Text>
        </Flex>
        <Button
          type="submit"
          colorScheme="blue"
          size="lg"
          w="full"
          fontWeight="bold"
          isLoading={isLoading}
          loadingText="Processing..."
        >
          Proceed to Checkout
        </Button>
      </VStack>
    </Box>
  );
};
