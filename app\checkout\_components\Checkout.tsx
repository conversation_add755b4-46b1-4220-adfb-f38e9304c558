'use client';

import { useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import type { User } from '@supabase/supabase-js';
import { Flex, Spinner, Box } from '@chakra-ui/react'; // Import Box for the container

import { useCart } from '@/providers/CartContext';
import { ReviewItemPanel } from './ReviewItemPanel';
import { AuthGuardPanel } from './AuthGuardPanel';
import { StatusPanel } from './StatusPanel';

/**
 * STEPS configuration object.
 * This is the single source of truth for the entire checkout flow.
 */
const STEPS = {
  review: { index: 0 },
  payment: { index: 1 },
  status: { index: 2 },
} as const;

type StepName = keyof typeof STEPS;

/**
 * A type guard that securely validates if a given string from the URL
 * is a valid, predefined step name.
 */
const isValidStep = (step: string): step is StepName => {
  return typeof step === 'string' && step in STEPS;
};

type CheckoutPageProps = {
  user: User | null;
};

/**
 * The main client-side checkout component, which acts as a "wizard" or "stepper".
 * It orchestrates the entire flow, managing the current step via URL state synchronization.
 */
const CheckoutPage = ({ user }: CheckoutPageProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const cart = useCart();

  const stepFromUrl = searchParams.get('step');
  const currentStepName = stepFromUrl as string;

  /**
   * This effect is the core engine for state management. It handles both
   * initial state resolution and ongoing persistence to localStorage.
   */
  useEffect(() => {
    if (isValidStep(currentStepName)) {
      try {
        localStorage.setItem('checkout_step', currentStepName);
      } catch (error) {
        console.error('Failed to save checkout step to localStorage', error);
      }
    } else {
      try {
        const stepFromStorage = localStorage.getItem(
          'checkout_step'
        ) as StepName;
        if (isValidStep(stepFromStorage)) {
          router.replace(`${pathname}?step=${stepFromStorage}`);
        } else {
          router.replace(`${pathname}?step=review`);
        }
      } catch (error) {
        console.error('Failed to read checkout step from localStorage', error);
        router.replace(`${pathname}?step=review`);
      }
    }
  }, [currentStepName, pathname, router]);

  /**
   * Navigates between checkout steps by updating the URL query parameter.
   */
  const handleNext = (nextStep: StepName) => {
    router.push(`${pathname}?step=${nextStep}`);
  };

  /**
   * Renders the UI for the current active step.
   * It includes a "render gate" to show a spinner until the URL is stable.
   */
  const renderCurrentStep = () => {
    if (!isValidStep(currentStepName)) {
      return (
        <Flex align="center" justify="center" minH="80vh">
          <Spinner size="xl" />
        </Flex>
      );
    }

    switch (currentStepName) {
      case 'review':
        return <ReviewItemPanel onNext={() => handleNext('payment')} />;

      case 'payment':
        return (
          <AuthGuardPanel
            user={user}
            cart={cart}
            onBack={() => handleNext('review')}
          />
        );

      case 'status':
        return <StatusPanel onBack={() => handleNext('payment')} />;
    }
  };

  // --- Jhon's Fix: Wrap the output in a Box with minH="100vh" ---
  // This ensures the component always takes up at least the full screen height.
  return <Box minH="100vh">{renderCurrentStep()}</Box>;
};

export default CheckoutPage;
