'use client';
import { V<PERSON><PERSON>ck, Button, Checkbox } from '@chakra-ui/react';

type ActionsProps = {
  onPlaceOrder: () => void;
  isLoading: boolean;
};

export const Actions = ({ onPlaceOrder, isLoading }: ActionsProps) => {
  return (
    <VStack spacing={4} align="stretch" w="full" pt={4}>
      <Checkbox colorScheme="blue">I agree to the terms & conditions.</Checkbox>
      <Button
        // Use onClick for client-side logic, not type="submit"
        onClick={onPlaceOrder}
        colorScheme="blue"
        size="lg"
        w="full"
        isLoading={isLoading}
        loadingText="Processing..."
      >
        Place Order
      </Button>
    </VStack>
  );
};
