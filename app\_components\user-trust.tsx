'use client';

import {
  Box,
  Container,
  SimpleGrid,
  VStack,
  HStack,
  Text,
  Heading,
  Icon,
  Image,
  Flex,
  Circle,
} from '@chakra-ui/react';
import { FaStar } from 'react-icons/fa';

// Data remains the same, assuming assets are correctly placed.
const partners = [
  { name: '<PERSON>on', logo: '/logos/noon.png' },
  { name: 'eXtra', logo: '/logos/extra.png' },
  { name: '<PERSON><PERSON>', logo: '/logos/namshi.png' },
];

const testimonial = {
  stars: 5,
  quote:
    'We saved 25 % on laptops, and Collab refunded us automatically when a vendor missed the target. Totally hassle-free!',
  author: '<PERSON>.',
};

export const UserTrust = () => {
  return (
    <Container
      maxW="container.xl"
      py={{ base: 10, md: 12 }}
      display={{ base: 'none', md: 'flex' }}
    >
      <Box
        bg="white"
        borderRadius="30px"
        border="1px solid"
        borderColor="gray.100"
        p={{ base: 6, md: 8, lg: 10 }}
        boxShadow="0 10px 50px -10px rgba(0, 0, 0, 0.07)"
      >
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={{ base: 10, lg: 12 }}>
          <VStack spacing={6} align="start" justify="center" h="full">
            <HStack>
              {Array(testimonial.stars)
                .fill('')
                .map((_, i) => (
                  <Icon as={FaStar} key={i} color="yellow.400" boxSize="22px" />
                ))}
            </HStack>
            <Text
              fontSize={{ base: 'lg', md: 'xl' }}
              lineHeight="1.7"
              color="gray.800"
            >
              {testimonial.quote}
            </Text>
            <VStack align="start" spacing={4} w="full" pt={2}>
              <Text fontWeight="bold" fontSize="md" color="gray.900">
                — {testimonial.author}
              </Text>
              <HStack spacing={2}>
                <Circle size="9px" bg="#34D399" />
                <Circle size="9px" bg="gray.200" />
                <Circle size="9px" bg="gray.200" />
              </HStack>
            </VStack>
          </VStack>

          <Flex align="center" justify="center">
            <VStack
              position="relative"
              bg="#F9FAFB" // A specific light gray for the inner panel
              borderRadius="20px"
              p={{ base: 6, md: 8 }}
              spacing={8}
              w="full"
              align="stretch"
              h="full"
            >
              <VStack spacing={2} flexDir="row" align="start">
                <Heading
                  as="h3"
                  w="full"
                  size="sm"
                  fontWeight="bold"
                  color="gray.900"
                  lineHeight="1.3"
                  flexGrow={2}
                >
                  Trusted by +100
                  <br />
                  Shoppers & Sellers
                </Heading>
                <HStack spacing={0} w="full" justify="end" align="start">
                  <Image
                    src="badges/badge.png"
                    alt="badge"
                    h={{ base: '1rem', md: '2.5rem', lg: '3.5rem' }}
                  />
                </HStack>
              </VStack>

              <HStack
                spacing={4}
                justify="start"
                align="end"
                position="absolute"
                bottom={4}
              >
                {partners.map(partner => (
                  <Flex
                    key={partner.name}
                    bg="white"
                    h="3rem"
                    w="7rem"
                    p={5}
                    borderRadius="12px"
                    align="center"
                    justify="center"
                    boxShadow="0 2px 10px -2px rgba(0, 0, 0, 0.08)"
                    border="1px solid"
                    borderColor="gray.100"
                  >
                    <Image
                      src={partner.logo}
                      alt={`${partner.name} logo`}
                      h={{ base: '2rem', md: '2rem' }}
                      w={{ base: '2rem', md: 'full' }}
                      objectFit="cover"
                    />
                  </Flex>
                ))}
              </HStack>
            </VStack>
          </Flex>
        </SimpleGrid>
      </Box>
    </Container>
  );
};

export default UserTrust;
