import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorForgotPasswordForm from './_components/VendorForgotPasswordForm';

export default async function VendorForgotPasswordPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect('/vendor/dashboard');
  }

  return <VendorForgotPasswordForm />;
}
