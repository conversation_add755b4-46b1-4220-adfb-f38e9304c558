import { Box, VStack, Image, Text, Link as ChakraLink } from '@chakra-ui/react';
import NextLink from 'next/link';

interface CategoryCardProps {
  imageUrl: string;
  itemCount: string;
  categoryName: string;
  href?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  imageUrl,
  itemCount,
  categoryName,
  href = '#',
}) => {
  return (
    <ChakraLink as={NextLink} href={href} _hover={{ textDecoration: 'none' }}>
      <VStack
        spacing={3}
        align="stretch"
        transition="all 0.2s"
        _hover={{ transform: 'translateY(-4px)' }}
      >
        <Box
          bg="gray.50"
          borderRadius="xl"
          p={6}
          display="flex"
          alignItems="center"
          justifyContent="center"
          h={{ base: '140px', md: '160px' }}
        >
          <Image
            src={imageUrl}
            alt={categoryName}
            maxH="100%"
            objectFit="cover"
          />
        </Box>
        <VStack spacing={0} align="start">
          <Text fontSize="sm" color="gray.500">
            {itemCount}
          </Text>
          <Text fontSize="md" fontWeight="medium" color="gray.800">
            {categoryName}
          </Text>
        </VStack>
      </VStack>
    </ChakraLink>
  );
};

export default CategoryCard;
