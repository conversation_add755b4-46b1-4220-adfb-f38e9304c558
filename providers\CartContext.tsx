'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from 'react';
import { useToast } from '@chakra-ui/react';
import { createClient } from '@/utils/supabase/client';
import { Circle } from '@/utils/supabase/queries';

export interface CartItem extends Circle {
  quantity: number;
}

interface CartContextType {
  item: CartItem | null;
  cartId: string | null;
  addItem: (item: Circle, quantity?: number) => void;
  removeItem: () => void;
  updateItemQuantity: (quantity: number) => void;
  clearCart: () => void;
  cartTotal: number;
  itemCount: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider = ({ children }: { children: ReactNode }) => {
  const [item, setItem] = useState<CartItem | null>(null);
  const [cartId, setCartId] = useState<string | null>(null);
  const toast = useToast({ position: 'top' });

  // This callback handles the "cart claiming" logic when a user logs in.
  const onAuthStateChange = useCallback(
    async (event: string) => {
      if (event === 'SIGNED_IN' && cartId && item) {
        console.log(
          `User signed in. Attempting to claim anonymous cart: ${cartId}`
        );
      }
    },
    [cartId, item, toast]
  );

  useEffect(() => {
    const supabase = createClient();
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(onAuthStateChange);

    return () => {
      subscription.unsubscribe();
    };
  }, [onAuthStateChange]);

  useEffect(() => {
    try {
      const storedItem = localStorage.getItem('collab_cart');
      // --- This key is now conceptually understood as the storage for our unified Cart ID ---
      const storedCartId = localStorage.getItem('collab_cart_id');
      if (storedItem) setItem(JSON.parse(storedItem));
      if (storedCartId) setCartId(storedCartId);
    } catch (error) {
      console.error('Failed to parse data from localStorage', error);
    }
  }, []);

  useEffect(() => {
    try {
      if (item) localStorage.setItem('collab_cart', JSON.stringify(item));
      else localStorage.removeItem('collab_cart');
    } catch (error) {
      console.error('Failed to save cart to localStorage', error);
    }
  }, [item]);

  useEffect(() => {
    try {
      if (cartId) localStorage.setItem('collab_cart_id', cartId);
      else localStorage.removeItem('collab_cart_id');
    } catch (error) {
      console.error('Failed to save cart ID to localStorage', error);
    }
  }, [cartId]);

  const addItem = (circle: Circle, quantity: number = 1) => {
    // --- This UUID is the single source of truth for the cart's identity ---
    // It will be used as the primary key ('id') in the database.
    if (!cartId) setCartId(crypto.randomUUID());
    const newItem: CartItem = { ...circle, quantity };
    setItem(newItem);
  };

  const removeItem = () => {
    setItem(null);
    toast({
      title: 'Item removed',
      status: 'warning',
      duration: 2000,
      isClosable: true,
    });
  };

  const updateItemQuantity = (quantity: number) => {
    if (!item) return;
    const updatedQty = Math.max(0, quantity);
    if (updatedQty === 0) removeItem();
    else setItem({ ...item, quantity: updatedQty });
  };

  const clearCart = () => {
    setItem(null);
    setCartId(null);
  };

  const cartTotal = item ? item.sale_price * item.quantity : 0;
  const itemCount = item ? item.quantity : 0;

  return (
    <CartContext.Provider
      value={{
        item,
        cartId,
        addItem,
        removeItem,
        updateItemQuantity,
        clearCart,
        cartTotal,
        itemCount,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) throw new Error('useCart must be used within a CartProvider');
  return context;
};
