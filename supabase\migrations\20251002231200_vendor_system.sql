-- Vendor System Migration - 20250103000002
-- This migration implements a complete vendor authentication and approval system

-- 1. Create approval status enum
CREATE TYPE public.approval_status_enum AS ENUM (
    'pending',
    'approved', 
    'rejected',
    'suspended'
);

-- 2. Add new columns to vendor table
ALTER TABLE public.vendor 
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS approval_status public.approval_status_enum DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS approval_notes TEXT,
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS date_of_incorporation DATE,
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS headline TEXT;

-- 3. Add unique constraint on user_id
ALTER TABLE public.vendor 
ADD CONSTRAINT vendor_user_id_unique UNIQUE (user_id);

-- 4. Create vendor_documents table
CREATE TABLE IF NOT EXISTS public.vendor_documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vendor_id TEXT NOT NULL REFERENCES public.vendor(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL CHECK (document_type IN (
        'business_license',
        'tax_certificate', 
        'commercial_registration',
        'identity_document',
        'bank_statement',
        'other'
    )),
    storage_object_id UUID NOT NULL REFERENCES storage.objects(id) ON DELETE CASCADE,
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMPTZ,
    verified_by UUID REFERENCES auth.users(id),
    verification_notes TEXT,
    expires_at DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4.1. Create vendor_versions table for version control
CREATE TABLE IF NOT EXISTS public.vendor_versions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vendor_id TEXT NOT NULL REFERENCES public.vendor(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'pending_approval', 'approved', 'rejected')),
    
    -- Vendor data fields (same as vendor table)
    name TEXT NOT NULL,
    slug TEXT NOT NULL,
    description TEXT,
    logo_url TEXT,
    contact_email TEXT NOT NULL,
    contact_phone TEXT,
    address JSONB,
    date_of_incorporation DATE,
    headline TEXT,
    
    -- Approval tracking
    submitted_at TIMESTAMPTZ,
    submitted_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ,
    approved_by UUID REFERENCES auth.users(id),
    rejected_at TIMESTAMPTZ,
    rejected_by UUID REFERENCES auth.users(id),
    rejection_reason TEXT,
    approval_notes TEXT,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(vendor_id, version_number),
    CONSTRAINT valid_email CHECK (contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_vendor_user_id ON public.vendor(user_id);
CREATE INDEX IF NOT EXISTS idx_vendor_approval_status ON public.vendor(approval_status);
CREATE INDEX IF NOT EXISTS idx_vendor_status ON public.vendor(status);
CREATE INDEX IF NOT EXISTS idx_vendor_slug ON public.vendor(slug);
CREATE INDEX IF NOT EXISTS idx_vendor_created_at ON public.vendor(created_at);

CREATE INDEX IF NOT EXISTS idx_vendor_documents_vendor_id ON public.vendor_documents(vendor_id);
CREATE INDEX IF NOT EXISTS idx_vendor_documents_type ON public.vendor_documents(document_type);
CREATE INDEX IF NOT EXISTS idx_vendor_documents_verified ON public.vendor_documents(is_verified);

CREATE INDEX IF NOT EXISTS idx_vendor_versions_vendor_id ON public.vendor_versions(vendor_id);
CREATE INDEX IF NOT EXISTS idx_vendor_versions_status ON public.vendor_versions(status);
CREATE INDEX IF NOT EXISTS idx_vendor_versions_version ON public.vendor_versions(vendor_id, version_number);
CREATE INDEX IF NOT EXISTS idx_vendor_versions_submitted ON public.vendor_versions(submitted_at);

-- 6. Enable RLS on new tables
ALTER TABLE public.vendor_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vendor_versions ENABLE ROW LEVEL SECURITY;

-- 7. Drop existing vendor policies to avoid conflicts
DROP POLICY IF EXISTS "Vendors can view their own data" ON public.vendor;
DROP POLICY IF EXISTS "Public can view active vendors" ON public.vendor;
DROP POLICY IF EXISTS "Admins can manage all vendors" ON public.vendor;

-- 8. Create new RLS policies for vendor table
CREATE POLICY "Vendors can view their own data" ON public.vendor
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Vendors can update their own data" ON public.vendor
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Public can view approved vendors" ON public.vendor
    FOR SELECT USING (approval_status = 'approved' AND status = 'active');

-- 9. Create RLS policies for vendor_documents table
CREATE POLICY "Vendors can manage their own documents" ON public.vendor_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.vendor 
            WHERE id = vendor_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all vendor documents" ON public.vendor_documents
    FOR SELECT USING (
        auth.jwt() ->> 'email' IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "Admins can update vendor documents" ON public.vendor_documents
    FOR UPDATE USING (
        auth.jwt() ->> 'email' IN ('<EMAIL>', '<EMAIL>')
    );

-- 9.1. Create RLS policies for vendor_versions table
CREATE POLICY "Vendors can manage their own versions" ON public.vendor_versions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.vendor 
            WHERE id = vendor_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all vendor versions" ON public.vendor_versions
    FOR SELECT USING (
        auth.jwt() ->> 'email' IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "Admins can update vendor versions" ON public.vendor_versions
    FOR UPDATE USING (
        auth.jwt() ->> 'email' IN ('<EMAIL>', '<EMAIL>')
    );

-- 10. Create database functions
CREATE OR REPLACE FUNCTION public.generate_vendor_slug(vendor_name TEXT)
RETURNS TEXT AS $$
DECLARE
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER := 0;
    exists_slug BOOLEAN;
BEGIN
    -- Convert name to slug format
    base_slug := lower(regexp_replace(trim(vendor_name), '[^a-zA-Z0-9\s]', '', 'g'));
    base_slug := regexp_replace(base_slug, '\s+', '-', 'g');
    base_slug := trim(both '-' from base_slug);
    
    final_slug := base_slug;
    
    -- Check for uniqueness and append counter if needed
    LOOP
        SELECT EXISTS(SELECT 1 FROM public.vendor WHERE slug = final_slug) INTO exists_slug;
        
        IF NOT exists_slug THEN
            RETURN final_slug;
        END IF;
        
        counter := counter + 1;
        final_slug := base_slug || '-' || counter;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.is_approved_vendor(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.vendor 
        WHERE user_id = user_uuid 
        AND approval_status = 'approved' 
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.get_vendor_by_user_id(user_uuid UUID)
RETURNS TABLE (
    id TEXT,
    name TEXT,
    slug TEXT,
    description TEXT,
    logo_url TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    address JSONB,
    status account_status_enum,
    approval_status approval_status_enum,
    approval_notes TEXT,
    approved_at TIMESTAMPTZ,
    approved_by UUID,
    date_of_incorporation DATE,
    version INTEGER,
    headline TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.id,
        v.name,
        v.slug,
        v.description,
        v.logo_url,
        v.contact_email,
        v.contact_phone,
        v.address,
        v.status,
        v.approval_status,
        v.approval_notes,
        v.approved_at,
        v.approved_by,
        v.date_of_incorporation,
        v.version,
        v.headline,
        v.created_at,
        v.updated_at
    FROM public.vendor v
    WHERE v.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.validate_vendor_data(
    vendor_name TEXT,
    vendor_email TEXT,
    vendor_phone TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Validate name (not empty, reasonable length)
    IF vendor_name IS NULL OR trim(vendor_name) = '' OR length(trim(vendor_name)) < 2 THEN
        RETURN FALSE;
    END IF;
    
    -- Validate email format
    IF vendor_email IS NULL OR vendor_email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
        RETURN FALSE;
    END IF;
    
    -- Validate phone if provided (basic format check)
    IF vendor_phone IS NOT NULL AND vendor_phone !~ '^[\+]?[0-9\s\-\(\)]{10,}$' THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 10.1. Version control functions
CREATE OR REPLACE FUNCTION public.create_vendor_version(
    p_vendor_id TEXT,
    p_name TEXT,
    p_contact_email TEXT,
    p_description TEXT DEFAULT NULL,
    p_logo_url TEXT DEFAULT NULL,
    p_contact_phone TEXT DEFAULT NULL,
    p_address JSONB DEFAULT NULL,
    p_date_of_incorporation DATE DEFAULT NULL,
    p_headline TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_version_number INTEGER;
    v_slug TEXT;
    v_version_id UUID;
BEGIN
    -- Get next version number
    SELECT COALESCE(MAX(version_number), 0) + 1 
    INTO v_version_number
    FROM public.vendor_versions 
    WHERE vendor_id = p_vendor_id;
    
    -- Generate slug
    v_slug := public.generate_vendor_slug(p_name);
    
    -- Validate data
    IF NOT public.validate_vendor_data(p_name, p_contact_email, p_contact_phone) THEN
        RAISE EXCEPTION 'Invalid vendor data provided';
    END IF;
    
    -- Create version
    INSERT INTO public.vendor_versions (
        vendor_id, version_number, name, slug, contact_email, description, logo_url,
        contact_phone, address, date_of_incorporation, headline
    ) VALUES (
        p_vendor_id, v_version_number, p_name, v_slug, p_contact_email, p_description, p_logo_url,
        p_contact_phone, p_address, p_date_of_incorporation, p_headline
    ) RETURNING id INTO v_version_id;
    
    RETURN v_version_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.submit_vendor_version_for_approval(
    p_version_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    v_vendor_id TEXT;
    v_user_id UUID;
BEGIN
    -- Get vendor and user info
    SELECT vv.vendor_id, v.user_id 
    INTO v_vendor_id, v_user_id
    FROM public.vendor_versions vv
    JOIN public.vendor v ON v.id = vv.vendor_id
    WHERE vv.id = p_version_id;
    
    -- Check if user owns this vendor
    IF v_user_id != auth.uid() THEN
        RAISE EXCEPTION 'Unauthorized: You can only submit your own vendor versions';
    END IF;
    
    -- Update version status
    UPDATE public.vendor_versions 
    SET 
        status = 'pending_approval',
        submitted_at = NOW(),
        submitted_by = auth.uid()
    WHERE id = p_version_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.approve_vendor_version(
    p_version_id UUID,
    p_approval_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    v_vendor_id TEXT;
    v_version_data RECORD;
BEGIN
    -- Check if user is admin
    IF auth.jwt() ->> 'email' NOT IN ('<EMAIL>', '<EMAIL>') THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can approve versions';
    END IF;
    
    -- Get version data
    SELECT * INTO v_version_data
    FROM public.vendor_versions
    WHERE id = p_version_id AND status = 'pending_approval';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Version not found or not pending approval';
    END IF;
    
    -- Update vendor table with approved version data
    UPDATE public.vendor SET
        name = v_version_data.name,
        slug = v_version_data.slug,
        description = v_version_data.description,
        logo_url = v_version_data.logo_url,
        contact_email = v_version_data.contact_email,
        contact_phone = v_version_data.contact_phone,
        address = v_version_data.address,
        date_of_incorporation = v_version_data.date_of_incorporation,
        headline = v_version_data.headline,
        version = v_version_data.version_number,
        updated_at = NOW()
    WHERE id = v_version_data.vendor_id;
    
    -- Update version status
    UPDATE public.vendor_versions SET
        status = 'approved',
        approved_at = NOW(),
        approved_by = auth.uid(),
        approval_notes = p_approval_notes
    WHERE id = p_version_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.reject_vendor_version(
    p_version_id UUID,
    p_rejection_reason TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF auth.jwt() ->> 'email' NOT IN ('<EMAIL>', '<EMAIL>') THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can reject versions';
    END IF;
    
    -- Update version status
    UPDATE public.vendor_versions SET
        status = 'rejected',
        rejected_at = NOW(),
        rejected_by = auth.uid(),
        rejection_reason = p_rejection_reason
    WHERE id = p_version_id AND status = 'pending_approval';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Version not found or not pending approval';
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.get_vendor_versions(p_vendor_id TEXT)
RETURNS TABLE (
    id UUID,
    version_number INTEGER,
    status TEXT,
    name TEXT,
    slug TEXT,
    description TEXT,
    logo_url TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    address JSONB,
    date_of_incorporation DATE,
    headline TEXT,
    submitted_at TIMESTAMPTZ,
    approved_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    rejection_reason TEXT,
    approval_notes TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vv.id, vv.version_number, vv.status, vv.name, vv.slug, vv.description,
        vv.logo_url, vv.contact_email, vv.contact_phone, vv.address,
        vv.date_of_incorporation, vv.headline, vv.submitted_at, vv.approved_at,
        vv.rejected_at, vv.rejection_reason, vv.approval_notes, vv.created_at, vv.updated_at
    FROM public.vendor_versions vv
    WHERE vv.vendor_id = p_vendor_id
    ORDER BY vv.version_number DESC;
END;
$$ LANGUAGE plpgsql;

-- 11. Create trigger function for auto-generating vendor slug
CREATE OR REPLACE FUNCTION public.auto_generate_vendor_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate slug if it's not already set
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug := public.generate_vendor_slug(NEW.name);
    END IF;
    
    -- Validate vendor data
    IF NOT public.validate_vendor_data(NEW.name, NEW.contact_email, NEW.contact_phone) THEN
        RAISE EXCEPTION 'Invalid vendor data provided';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Create trigger for vendor table
CREATE OR REPLACE TRIGGER auto_generate_vendor_slug_trigger
    BEFORE INSERT ON public.vendor
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_generate_vendor_slug();

-- 13. Create trigger for updating updated_at on vendor_documents
CREATE OR REPLACE TRIGGER update_vendor_documents_updated_at
    BEFORE UPDATE ON public.vendor_documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 13.1. Create trigger for updating updated_at on vendor_versions
CREATE OR REPLACE TRIGGER update_vendor_versions_updated_at
    BEFORE UPDATE ON public.vendor_versions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 14. Update existing vendor records to set approval_status to pending where null
UPDATE public.vendor 
SET approval_status = 'pending' 
WHERE approval_status IS NULL;

-- 15. Grant permissions to authenticated role
GRANT ALL ON FUNCTION public.generate_vendor_slug(TEXT) TO authenticated;
GRANT ALL ON FUNCTION public.is_approved_vendor(UUID) TO authenticated;
GRANT ALL ON FUNCTION public.get_vendor_by_user_id(UUID) TO authenticated;
GRANT ALL ON FUNCTION public.validate_vendor_data(TEXT, TEXT, TEXT) TO authenticated;

-- 15.1. Grant permissions for version control functions
GRANT ALL ON FUNCTION public.create_vendor_version(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, JSONB, DATE, TEXT) TO authenticated;
GRANT ALL ON FUNCTION public.submit_vendor_version_for_approval(UUID) TO authenticated;
GRANT ALL ON FUNCTION public.approve_vendor_version(UUID, TEXT) TO authenticated;
GRANT ALL ON FUNCTION public.reject_vendor_version(UUID, TEXT) TO authenticated;
GRANT ALL ON FUNCTION public.get_vendor_versions(TEXT) TO authenticated;

-- 16. Grant table permissions
GRANT ALL ON TABLE public.vendor_documents TO authenticated;
GRANT ALL ON TABLE public.vendor_documents TO service_role;

GRANT ALL ON TABLE public.vendor_versions TO authenticated;
GRANT ALL ON TABLE public.vendor_versions TO service_role;

-- 17. Add comments for documentation
COMMENT ON TABLE public.vendor_documents IS 'Stores vendor verification documents and their verification status. Documents are stored in Supabase storage with folder structure: vendor/{vendor_id}/{document_type}/';
COMMENT ON COLUMN public.vendor_documents.document_type IS 'Type of document (business_license, tax_certificate, etc.)';
COMMENT ON COLUMN public.vendor_documents.storage_object_id IS 'Reference to storage.objects table - documents stored in vendor/{vendor_id}/{document_type}/ folder structure';
COMMENT ON COLUMN public.vendor_documents.is_verified IS 'Whether the document has been verified by admin';
COMMENT ON COLUMN public.vendor_documents.expires_at IS 'Document expiration date if applicable';

COMMENT ON COLUMN public.vendor.user_id IS 'Links vendor to auth user account';
COMMENT ON COLUMN public.vendor.approval_status IS 'Overall vendor approval state';
COMMENT ON COLUMN public.vendor.approval_notes IS 'Admin notes for approval decisions';
COMMENT ON COLUMN public.vendor.approved_at IS 'Timestamp of approval/rejection';
COMMENT ON COLUMN public.vendor.approved_by IS 'Admin who made the decision';
COMMENT ON COLUMN public.vendor.date_of_incorporation IS 'Business incorporation date';
COMMENT ON COLUMN public.vendor.headline IS 'Vendor tagline/headline';
COMMENT ON COLUMN public.vendor.version IS 'Version number for vendor data changes';

-- 17.1. Add comments for vendor_versions table
COMMENT ON TABLE public.vendor_versions IS 'Version control system for vendor data changes with approval workflow';
COMMENT ON COLUMN public.vendor_versions.version_number IS 'Sequential version number for each vendor';
COMMENT ON COLUMN public.vendor_versions.status IS 'Version status: draft, pending_approval, approved, rejected';
COMMENT ON COLUMN public.vendor_versions.submitted_at IS 'When the version was submitted for approval';
COMMENT ON COLUMN public.vendor_versions.approved_at IS 'When the version was approved by admin';
COMMENT ON COLUMN public.vendor_versions.rejected_at IS 'When the version was rejected by admin';
COMMENT ON COLUMN public.vendor_versions.rejection_reason IS 'Reason for rejection provided by admin';
COMMENT ON COLUMN public.vendor_versions.approval_notes IS 'Admin notes about the approval decision';