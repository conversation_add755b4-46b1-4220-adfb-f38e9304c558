'use client';
import React, { useMemo } from 'react';
import { Box, Text, VStack, Icon, Flex } from '@chakra-ui/react';
import { FaShoppingCart } from 'react-icons/fa';
import type { PricingTier } from '@/utils/supabase/queries'; // Assuming the type is available for import

interface SemiCircleCountDownProps {
  value?: number;
  max?: number;
  pricingTiers?: PricingTier[];
}

const SemiCircleCountDown: React.FC<SemiCircleCountDownProps> = ({
  value = 0,
  max = 1000,
  pricingTiers = [],
}) => {
  const colors = {
    green: '#25D366',
    yellow: '#FFA500',
    red: '#FF4500',
    track: '#F0F2F5',
    darkGray: '#A0AEC0',
    dotBorder: '#E9E9E9',
  };

  // --- DYNAMIC SEGMENT GENERATION ---
  const segments = useMemo(() => {
    // A prerequisite: sort tiers by threshold to ensure correct order.
    const sortedTiers = [...pricingTiers].sort(
      (a, b) => (a.threshold || 0) - (b.threshold || 0)
    );

    let lastThreshold = 0;
    // Create segments for each pricing tier.
    const calculatedSegments = sortedTiers.map(tier => {
      const segment = {
        start: lastThreshold,
        end: tier.threshold!,
        label: String(tier.threshold!),
      };
      lastThreshold = tier.threshold!;
      return segment;
    });

    // If the last tier's threshold doesn't reach the max, add a final segment.
    if (lastThreshold < max) {
      calculatedSegments.push({
        start: lastThreshold,
        end: max,
        label: String(max),
      });
    }

    // Fallback for when no tiers are provided, creating a single segment up to max.
    if (calculatedSegments.length === 0) {
      return [{ start: 0, end: max, label: String(max) }];
    }

    return calculatedSegments;
  }, [pricingTiers, max]);

  const clampedValue = Math.min(Math.max(value, 0), max);
  const isFull = clampedValue === max;

  const size = 220;
  const strokeWidth = 10;
  const radius = (size - strokeWidth) / 2;
  const circumference = Math.PI * radius;
  const center = size / 2;

  const pathD = `M ${center - radius},${center} A ${radius},${radius} 0 0 1 ${center + radius},${center}`;

  const progressPercentage = clampedValue / max;
  const maskOffset = circumference * (1 - progressPercentage);

  const angle = (1 - progressPercentage) * 180;
  const angleRad = (angle * Math.PI) / 180;
  const dotX = center + radius * Math.cos(angleRad);
  const dotY = center - radius * Math.sin(angleRad);

  const maskId = useMemo(
    () => `progress-mask-${Math.random().toString(36).substr(2, 9)}`,
    []
  );

  const getGradientColor = (progress: number) => {
    if (progress <= 0.3) return colors.green;
    if (progress <= 0.6) return colors.yellow;
    return colors.red;
  };

  const dotColor = isFull
    ? colors.darkGray
    : getGradientColor(progressPercentage);

  return (
    <VStack w="full" spacing={2.5}>
      <Box position="relative" w="full" h="110px">
        <VStack
          position="absolute"
          top="70%"
          left="50%"
          transform="translate(-50%, -50%)"
          spacing={0}
          zIndex={1}
        >
          <Icon as={FaShoppingCart} color="gray.400" boxSize="5" />
          <Text
            fontSize={{ base: 'xl', md: '3xl' }}
            fontWeight="bold"
            lineHeight="1.1"
          >
            {isFull ? max : clampedValue}
          </Text>
          <Text fontSize="sm" color="gray.500">
            Total Joiners
          </Text>
        </VStack>

        <Box position="absolute" top={0} left={0} w="full" h="full" zIndex={20}>
          <svg width="100%" height="100%" viewBox={`0 0 ${size} 100`}>
            <defs>
              <linearGradient
                id={`gradient-${maskId}`}
                x1="0%"
                y1="0%"
                x2="100%"
                y2="0%"
              >
                <stop offset="0%" stopColor={colors.green} />
                <stop offset="50%" stopColor={colors.yellow} />
                <stop offset="100%" stopColor={colors.red} />
              </linearGradient>

              <mask id={maskId}>
                <path
                  d={pathD}
                  fill="none"
                  stroke="white"
                  strokeWidth={strokeWidth}
                  strokeDasharray={circumference}
                  strokeDashoffset={maskOffset}
                  strokeLinecap="round"
                />
              </mask>
            </defs>

            <path
              d={pathD}
              fill="none"
              stroke={isFull ? colors.darkGray : colors.track}
              strokeWidth={strokeWidth}
              strokeLinecap="round"
            />

            {!isFull && (
              <g mask={`url(#${maskId})`}>
                <path
                  d={pathD}
                  fill="none"
                  stroke={`url(#gradient-${maskId})`}
                  strokeWidth={strokeWidth}
                  strokeLinecap="round"
                />
              </g>
            )}

            {!isFull && clampedValue > 0 && (
              <circle
                cx={dotX}
                cy={dotY}
                r={8}
                fill="white"
                stroke={dotColor}
                strokeWidth={3}
              />
            )}
            {isFull && (
              <circle
                cx={size - strokeWidth / 2}
                cy={center}
                r={8}
                fill={colors.darkGray}
                stroke={colors.dotBorder}
                strokeWidth={3}
              />
            )}
          </svg>
        </Box>
      </Box>

      {/* Unified gradient bar with DYNAMIC labels */}
      <VStack w="full" spacing={0} position="relative">
        <Box
          w="full"
          h="4px"
          borderRadius="sm"
          bgGradient="linear(to-r, green.400, yellow.400, red.400)"
        />

        <Flex w="full" position="relative" h="20px">
          {segments.map(segment => {
            const segmentWidth = 100 / segments.length;

            return (
              <Box
                key={segment.end} // Using the end value as a key, since thresholds should be unique
                textAlign="center"
                width={`${segmentWidth}%`}
                pr={1} // Add padding to align the text with the segment end
              >
                <Text fontSize="xs" color="gray.500">
                  {segment.label}
                </Text>
              </Box>
            );
          })}
        </Flex>
      </VStack>
    </VStack>
  );
};

export default SemiCircleCountDown;
