-------------------------------------------------
-- 1) Create Extensions
-------------------------------------------------
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_jsonschema";

-------------------------------------------------
-- 2) Define Enum Types
-------------------------------------------------
DO $$ BEGIN
    CREATE TYPE public.account_status_enum AS ENUM ('active', 'inactive', 'pending', 'suspended');
    CREATE TYPE public.product_status_enum AS ENUM ('draft', 'active', 'inactive');
    CREATE TYPE public.circle_status_enum AS ENUM ('draft', 'active', 'closed', 'cancelled', 'completed');
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

CREATE TABLE IF NOT EXISTS public.vendor (
    id UUID PRIMARY KEY,
    vendor_name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    logo_url TEXT,
    contact_email TEXT NOT NULL,
    contact_phone TEXT,
    address JSONB,
    status public.account_status_enum NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_email CHECK (contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

alter table public.vendor enable row level security;

CREATE TABLE IF NOT EXISTS public.product (
    -- Main Fields
    CSIN TEXT PRIMARY KEY UNIQUE NOT NULL, -- Collab Serial Identification Number
    main_category_id TEXT NOT NULL, -- Main Category will be stored as a string
    categories TEXT[],  -- Other Categories will be stored as an array
    name TEXT NOT NULL,     -- Product Name
    title TEXT NOT NULL,    -- Product Title
    description TEXT NOT NULL, -- Product Description
    brand TEXT NOT NULL, -- Brand Name
    model TEXT NOT NULL,  -- Model Number
    sku TEXT NOT NULL, -- Stock Keeping Unit
    upc TEXT NOT NULL, -- Universal Product Code

    -- Pricing 
    market_price NUMERIC(15,2) NOT NULL,
    currency TEXT DEFAULT 'SAR',

    -- Shipping
    weight_in_kg NUMERIC(15,2) NOT NULL,
    dimension_height_in_cm NUMERIC(15,2) NOT NULL,
    dimension_width_in_cm NUMERIC(15,2) NOT NULL,  
    dimension_length_in_cm NUMERIC(15,2) NOT NULL,
    
    country_of_origin TEXT NOT NULL,
    warranty TEXT,

    -- SEO
    slug TEXT NOT NULL UNIQUE,
    meta_title TEXT,
    meta_description TEXT,
    meta_keywords TEXT[],

    search_vector tsvector,
    status public.product_status_enum NOT NULL DEFAULT 'draft',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);



CREATE TABLE IF NOT EXISTS public.sale_circle (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    status public.circle_status_enum NOT NULL DEFAULT 'draft',
    description TEXT,
    vendor_id UUID REFERENCES public.vendor(id),
    product_id TEXT REFERENCES public.product(CSIN),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    sale_price NUMERIC(15,2) NOT NULL,
    pricing_tiers JSONB,
    max_participants INT NOT NULL,
    min_quantity INT NOT NULL DEFAULT 1,
    max_quantity INT NOT NULL DEFAULT 1,
    current_participants INT DEFAULT 0,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    CONSTRAINT valid_dates CHECK (end_date > start_date),
    CONSTRAINT valid_current CHECK (current_participants <= max_participants)
);

-- Create user_profile table
CREATE TABLE IF NOT EXISTS public.user_profile (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    address JSONB,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profile (id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Add RLS policies for user_profile
alter table public.user_profile enable row level security;

-- Users can view their own profile
create policy "Users can view their own profile."
    on user_profile for select
    using ( auth.uid() = id );

-- Users can update their own profile
create policy "Users can update their own profile."
    on user_profile for update
    using ( auth.uid() = id );

-- Create index for faster lookups
CREATE INDEX idx_user_profile_id ON public.user_profile(id);

alter table public.product enable row level security;
alter table public.sale_circle enable row level security;

alter policy "Circles are visible to everyone."
on public.sale_circle for select
to public
using ( true );

alter policy "Public Products are visible to everyone."
on public.product for select
to public
using ( true );

-- Indexes
CREATE INDEX idx_product_search ON public.product USING GIN (search_vector);
CREATE INDEX idx_circle_dates ON public.sale_circle (start_date, end_date);
CREATE INDEX idx_circle_status ON public.sale_circle (status);

-- Triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
DO $$ 
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
    LOOP
        EXECUTE format('
            CREATE TRIGGER set_updated_at
            BEFORE UPDATE ON public.%I
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column()', t);
    END LOOP;
END $$;

-- Full Text Search Trigger for Products
CREATE OR REPLACE FUNCTION product_search_trigger() RETURNS trigger AS $$
BEGIN
    NEW.search_vector :=
        setweight(to_tsvector('english', COALESCE(NEW.name, '')), 'A') ||
        setweight(to_tsvector('english', COALESCE(NEW.description, '')), 'B');
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

CREATE TRIGGER product_search_update
    BEFORE INSERT OR UPDATE ON public.product
    FOR EACH ROW
    EXECUTE FUNCTION product_search_trigger();
