import { Box, Heading, VStack, Divider } from '@chakra-ui/react';
import { CartItem as CartItemType } from '@/providers/CartContext';
import { CartItem } from './CartItem';
interface CartItemsCardProps {
  items: CartItemType[];
}

const CartItemsCard = ({ items }: CartItemsCardProps) => {
  return (
    <Box bg="white" p={6} borderRadius="xl" boxShadow="base">
      <VStack align="stretch" spacing={5}>
        <Heading as="h2" size="md" fontWeight="semibold">
          Order Summary
        </Heading>
        <Divider />
        {items.map(item => (
          <CartItem key={item.id} item={item} />
        ))}
      </VStack>
    </Box>
  );
};

export { CartItemsCard };
