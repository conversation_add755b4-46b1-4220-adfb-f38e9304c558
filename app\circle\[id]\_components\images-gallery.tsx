'use client';

import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  IconButton,
  Image as ChakraImage,
  AspectRatio,
} from '@chakra-ui/react';
import { FiChevronLeft, FiChevronRight, FiImage } from 'react-icons/fi';

interface ProductImageGalleryProps {
  images: string[];
  alt?: string | undefined;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  images,
  alt,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);

  // Guard against no images
  if (!images || images.length === 0) {
    return (
      <AspectRatio ratio={1} w="full">
        <Box
          bg="gray.100"
          borderRadius="xl"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <FiImage size="48px" color="gray.400" />
        </Box>
      </AspectRatio>
    );
  }

  const handleNext = () => {
    setActiveIndex(prev => (prev + 1) % images.length);
  };

  const handlePrev = () => {
    setActiveIndex(prev => (prev - 1 + images.length) % images.length);
  };

  return (
    <VStack align="stretch" spacing={4}>
      {/* Main Image Viewer */}
      <Box position="relative" w="full" overflow="hidden" borderRadius="xl">
        <ChakraImage
          src={images[activeIndex]}
          objectFit="contain"
          w="15rem"
          h="15rem"
          mx="auto"
        />

        {/* Navigation Arrows */}
        <IconButton
          aria-label="Previous image"
          icon={<FiChevronLeft />}
          onClick={handlePrev}
          isRound
          size="sm"
          position="absolute"
          left={3}
          top="50%"
          transform="translateY(-50%)"
          bg="whiteAlpha.800"
          _hover={{ bg: 'white' }}
          boxShadow="md"
        />
        <IconButton
          aria-label="Next image"
          icon={<FiChevronRight />}
          onClick={handleNext}
          isRound
          size="sm"
          position="absolute"
          right={3}
          top="50%"
          transform="translateY(-50%)"
          bg="whiteAlpha.800"
          _hover={{ bg: 'white' }}
          boxShadow="md"
        />
      </Box>

      {/* Thumbnails */}
      <HStack spacing={3} justify="center">
        {images.map((src, index) => (
          <Box
            key={src}
            as="button"
            w="64px"
            h="64px"
            p="2px"
            borderRadius="md"
            border="2px solid"
            borderColor={activeIndex === index ? 'orange.400' : 'gray.200'}
            onClick={() => setActiveIndex(index)}
            transition="border-color 0.2s"
          >
            <ChakraImage
              src={src}
              alt={`${alt} thumbnail ${index + 1}`}
              w="3rem"
              h="3rem"
              objectFit="contain"
              borderRadius="sm"
            />
          </Box>
        ))}
      </HStack>
    </VStack>
  );
};

export { ProductImageGallery };
