'use server';
import { createClient } from './server';
import { Database } from '@/types/supabase';
export type PricingTier = {
  price: number;
  threshold: number;
};

export type CircleRow = Database['public']['Tables']['circle']['Row'] & {
  pricing_tiers: PricingTier[];
};

export type Circle = CircleRow & {
  product: {
    product_image: string | null;
    about: string | null;
    name: string | null;
    market_price: number | null;
    main_category: { slug: string; name: string } | null;
    category_1: { slug: string; name: string } | null;
    category_2: { slug: string; name: string } | null;
    images: string[];
  };
} & {
  vendor: {
    id: string;
    name: string;
    slug: string;
  };
};

export type Category = Database['public']['Tables']['category']['Row'] & {
  subcategories?: Category[];
};

const CIRCLE_PRODUCT_VENDOR_QUERY = `*,
  product:product_id(
    product_image,
    about,
    name,
    market_price,
    main_category:category!product_main_category_fkey (slug, name ),
    category_1:category!product_category_1_fkey (slug, name ),
    category_2:category!product_category_2_fkey (slug, name )
  ),
  vendor:vendor_id(id, name, slug)
`;

export async function getCategories(): Promise<Category[]> {
  try {
    const supabase = await createClient();
    const { data: categories, error } = await supabase
      .from('category')
      .select(
        `
        id, slug, name, category_image, created_at, parent,
            subcategories:category!parent (
              id, slug, name, category_image, created_at, parent, 
              subcategories:category!parent (
                id, slug, name, category_image, created_at, parent,
                subcategories:category!parent (
                  id, slug, name, category_image, created_at, parent
                )
              )
            )
      `
      )
      .is('parent', null)
      .order('slug', { ascending: true });
    if (error) throw error;
    return (categories as unknown as Category[]) || [];
  } catch (err) {
    console.error('getCategories failed:', err);
    return [];
  }
}

export async function getCircleById(id: string): Promise<Circle | null> {
  try {
    const supabase = await createClient();

    const { data: circle, error: circleError } = await supabase
      .from('circle')
      .select(
        `
        *,
        product:product_id(
          product_image,
          about,
          name,
          market_price,
          main_category:category!product_main_category_fkey (slug, name),
          category_1:category!product_category_1_fkey (slug, name),
          category_2:category!product_category_2_fkey (slug, name)
        ),
        vendor:vendor_id(id, name, slug)
      `
      )
      .eq('id', Number(id))
      .single();

    if (circleError) {
      console.error('Error fetching circle and product details:', circleError);
      return null;
    }

    if (!circle) {
      return null;
    }

    if (circle.product_id) {
      const imageUrls = await getProductImages(circle.product_id, 5);

      // Attach the array of image URLs to the nested product object.
      if (circle.product) {
        (circle.product as Circle['product']).images = imageUrls;
      }
    } else {
      // Ensure the images array exists, even if empty, to prevent client-side errors.
      if (circle.product) {
        (circle.product as Circle['product']).images = [];
      }
    }

    return circle as unknown as Circle;
  } catch (error) {
    console.error('Failed to fetch circle by ID:', error);
    return null;
  }
}
export async function getCircles(): Promise<Circle[] | null> {
  try {
    const supabase = await createClient();

    const { data: circles, error } = await supabase
      .from('circle')
      .select(CIRCLE_PRODUCT_VENDOR_QUERY)
      .limit(20)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching circle and product details:', error);
      return null;
    }

    return (circles as unknown as Circle[]) || null;
  } catch (error) {
    console.error('Failed to fetch circle by ID:', error);
    return null;
  }
}
export async function getRelatedCircles(
  excludeId: string,
  limit: number = 10
): Promise<Circle[]> {
  try {
    const supabase = await createClient();

    // --- Step 1: Fetch the categories of the source product ---
    const { data: currentCircleProduct, error: productError } = await supabase
      .from('circle')
      .select(
        `
        product:product_id(
          main_category,
          category_1,
          category_2
        )
      `
      )
      .eq('id', Number(excludeId))
      .single();

    if (productError || !currentCircleProduct?.product) {
      console.error(
        `Could not find product for circle ID ${excludeId} to get related categories:`,
        productError
      );
      return []; // Cannot find related circles if we don't know the source categories.
    }

    // --- Step 2 (NEW LOGIC): Find all product IDs that match these categories ---
    const { main_category, category_1, category_2 } =
      currentCircleProduct.product;
    const categoryIds = [main_category, category_1, category_2].filter(
      (id): id is string => id !== null
    );

    if (categoryIds.length === 0) {
      return []; // No categories to match against.
    }
    const { data: relatedProducts, error: relatedProductsError } =
      await supabase
        .from('product')
        .select('csin')
        .or(
          `main_category.in.(${categoryIds.join(',')}),` +
            `category_1.in.(${categoryIds.join(',')}),` +
            `category_2.in.(${categoryIds.join(',')})`
        );

    if (relatedProductsError) {
      console.error(
        'Error fetching related product IDs:',
        relatedProductsError
      );
      return [];
    }

    const relatedProductIds = relatedProducts.map(p => p.csin);
    if (relatedProductIds.length === 0) {
      return []; // No other products found in these categories.
    }

    // --- Step 3 (NEW LOGIC): Fetch circles using the list of product IDs ---
    const { data: circles, error: relatedError } = await supabase
      .from('circle')
      .select(CIRCLE_PRODUCT_VENDOR_QUERY)
      .eq('status', 'active')
      .neq('id', Number(excludeId)) // Exclude the original circle .. comment this line to test
      .in('product_id', relatedProductIds) // Filter using our clean list of product IDs
      .limit(limit);

    if (relatedError) {
      console.error('Error fetching related circles:', relatedError);
      return [];
    }

    if (!circles || circles.length === 0) {
      return [];
    }
    // --- Step 4: Enrich the results with product images concurrently ---

    const allImagesPromises: Promise<Circle['product']['images']>[] =
      circles.map(circle =>
        circle.product_id
          ? getProductImages(circle.product_id, 5)
          : Promise.resolve([] as string[])
      );

    const allImagesResults = await Promise.all(allImagesPromises);

    const circlesWithImages = circles.map((circle, index: number) => {
      const imageUrls: string[] = allImagesResults[index] || [];
      const productWithImages = circle.product
        ? { ...circle.product, images: imageUrls }
        : null;

      return {
        ...circle,
        product: productWithImages,
      };
    });

    return circlesWithImages as unknown as Circle[];
  } catch (error) {
    console.error('Failed to fetch related circles:', error);
    return [];
  }
}

// =========================================================================
// ===                     FOR TODAY'S CIRCLES                           ===
// =========================================================================
export async function getTodayCircles(): Promise<Circle[]> {
  const supabase = await createClient();
  try {
    const { data: todayCircleRelations, error: idError } = await supabase
      .from('today_circles')
      .select('circle_id');

    if (idError) {
      console.log('Error fetching today_circles IDs:', idError.message);
      throw idError;
    }

    if (!todayCircleRelations || todayCircleRelations.length === 0) {
      return [];
    }

    const circleIds: number[] = todayCircleRelations
      .map(item => item.circle_id)
      .filter(v => v !== null);

    const { data: circles, error: circleError } = await supabase
      .from('circle')
      .select(CIRCLE_PRODUCT_VENDOR_QUERY)
      .in('id', circleIds);
    if (circleError) {
      console.log('Error fetching circle details:', circleError.message);
      throw circleError;
    }

    if (!circles || circles.length === 0) {
      return [];
    }

    // Use the shared helper to fetch image URLs for each product in parallel.
    const allImagesPromises: Promise<Circle['product']['images']>[] =
      circles.map(circle =>
        circle.product_id
          ? getProductImages(circle.product_id, 5)
          : Promise.resolve([] as string[])
      );

    const allImagesResults = await Promise.all(allImagesPromises);

    const circlesWithImages = circles.map((circle, index: number) => {
      const imageUrls: string[] = allImagesResults[index] || [];
      const productWithImages = circle.product
        ? { ...circle.product, images: imageUrls }
        : null;

      return {
        ...circle,
        product: productWithImages,
      };
    });

    return (circlesWithImages as unknown as Circle[]) || [];
  } catch (err) {
    console.log('getTodayCircles failed:', err);
    return [];
  }
}
export async function getEndSoonCircles(): Promise<{
  data: Circle[];
  count: number | null;
}> {
  const supabase = await createClient();
  try {
    // 1. Get the current timestamp.
    const now = new Date();
    // 2. Calculate the timestamp for 7 days in the future.
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    // This query now correctly fetches circles ending within the next 7 days.
    const {
      data: circles,
      error,
      count,
    } = await supabase
      .from('circle')
      .select(CIRCLE_PRODUCT_VENDOR_QUERY, { count: 'exact' })
      .eq('status', 'active')
      .gte('end_date', now.toISOString()) // Must end after right now...
      .lte('end_date', sevenDaysFromNow.toISOString()) // ...but before 7 days from now.
      .order('end_date', { ascending: true }); // Soonest-ending circles first.

    if (error) {
      console.error('Supabase error fetching end-soon circles:', error);
      return { data: [], count: null };
    }

    if (!circles || circles.length === 0) {
      return { data: [], count: 0 };
    }

    // Use the shared helper to fetch image URLs for each product in parallel.
    const allImagesPromises: Promise<Circle['product']['images']>[] =
      circles.map(circle =>
        circle.product_id
          ? getProductImages(circle.product_id, 5)
          : Promise.resolve([] as string[])
      );

    const allImagesResults = await Promise.all(allImagesPromises);

    // Map over the original circles and merge the image data.
    const circlesWithImages = circles.map((circle, index: number) => {
      const imageUrls: string[] = allImagesResults[index] || [];
      const productWithImages = circle.product
        ? { ...circle.product, images: imageUrls }
        : null;

      return {
        ...circle,
        product: productWithImages,
      };
    });

    return { data: circlesWithImages as unknown as Circle[], count };
  } catch (err) {
    console.error('getEndSoonCircles failed:', err);
    return { data: [], count: null };
  }
}

/**
 * Fetch public image URLs for a product in the 'products-images' bucket.
 * Accepts an optional supabase client to avoid re-creating it when the caller already has one.
 * Returns an array of public URLs (possibly empty).
 */
export async function getProductImages(
  productId: string | number | null | undefined,
  limit: number = 5
): Promise<string[]> {
  try {
    if (!productId) return [];
    const pid = String(productId);

    const supabase = await createClient();

    const { data: imageFiles, error: imageError } = await supabase.storage
      .from('product')
      .list(pid, {
        limit,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' },
      });

    if (imageError) {
      console.error(`Error listing images for product ${pid}:`, imageError);
      return [];
    }

    if (!imageFiles || imageFiles.length === 0) {
      return [];
    }

    const imageUrls = imageFiles
      .map(file => {
        const { data } = supabase.storage
          .from('product')
          .getPublicUrl(`${pid}/${file.name}`);
        return data?.publicUrl ?? '';
      })
      .filter(Boolean); // remove empty strings

    return imageUrls;
  } catch (err) {
    console.error('getProductImages failed:', err);
    return [];
  }
}
