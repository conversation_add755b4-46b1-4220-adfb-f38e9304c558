'use client';

import { CSSProperties, useState } from 'react';
import {
  Box,
  Flex,
  Heading,
  Spacer,
  Link as ChakraLink,
  Spinner,
} from '@chakra-ui/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import NextLink from 'next/link';
import { FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { CategoryCard } from '@/components/cards';

/*
 * For now categoryData is hard coded as an array
 * later we display only main categorie where category.parent_id == null
 */
const categoryData = [
  {
    id: 1,
    categoryName: 'Electronics & Technology',
    itemCount: '4 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//laptop.png',
  },
  {
    id: 2,
    categoryName: 'Fashion & Accessories',
    itemCount: '4 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//fashion.png',
  },
  {
    id: 3,
    categoryName: 'Home & Kitchen',
    itemCount: '4 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//home.png',
  },
  {
    id: 4,
    categoryName: 'Beauty & Personal Care',
    itemCount: '4 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//beauty.png',
  },
  {
    id: 5,
    categoryName: 'Sports & Outdoors',
    itemCount: '4 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//sport.png',
  },
  {
    id: 6,
    categoryName: 'Toys & Games',
    itemCount: '3 k items',
    imageUrl:
      'https://spfgzmdtgibgscqvkiig.supabase.co/storage/v1/object/public/category-image//toys.png',
  },
];

const ShopByCategory = () => {
  const [isSwiperReady, setIsSwiperReady] = useState(false);

  return (
    <Box
      as="section"
      bg="white"
      py={8}
      maxW="container.xl"
      mx="auto"
      borderRadius="xl"
      shadow="md"
    >
      <Box maxW="1400px" mx="auto" px={{ base: 4, md: 8 }}>
        <Flex align="center" mb={6}>
          <Heading
            as="h2"
            fontSize={{ base: 'xl', md: '2xl' }}
            fontWeight="bold"
          >
            Shop by Category
          </Heading>
          <Spacer />
          <ChakraLink
            as={NextLink}
            href="/categories"
            border="1px"
            borderColor="gray.300"
            px={{ base: 3, md: 4 }}
            py={2}
            borderRadius="lg"
            _hover={{ textDecoration: 'none', bg: 'gray.50' }}
            display="flex"
            alignItems="center"
            gap={2}
          >
            <Box
              as="span"
              fontSize={{ base: 'sm', md: 'md' }}
              fontWeight="medium"
            >
              All Categories
            </Box>
            <FiChevronRight size={16} />
          </ChakraLink>
        </Flex>

        {/* --- CORE UPGRADE: Spinner and visibility tied to Swiper state --- */}
        <Box
          position="relative"
          mx={{ base: -4, md: -8 }}
          // Reserve space for the carousel to prevent layout shift.
          // Category cards are shorter, so we need less height.
          minH="220px"
        >
          {!isSwiperReady && (
            <Flex
              align="center"
              justify="center"
              h="220px"
              position="absolute"
              w="full"
            >
              <Spinner
                size="xl"
                thickness="4px"
                speed="0.65s"
                emptyColor="gray.200"
                color="primary"
              />
            </Flex>
          )}

          <Box
            opacity={isSwiperReady ? 1 : 0}
            transition="opacity 0.4s ease-in-out"
          >
            <Swiper
              // The onSwiper callback signals when the component is ready.
              onSwiper={() => setIsSwiperReady(true)}
              modules={[Navigation]}
              spaceBetween={16}
              slidesPerView={'auto'}
              breakpoints={{
                320: { slidesPerView: 2.2, spaceBetween: 12 },
                640: { slidesPerView: 3.5, spaceBetween: 16 },
                768: { slidesPerView: 4.5, spaceBetween: 16 },
                1280: { slidesPerView: 5.5, spaceBetween: 20 },
              }}
              navigation={{
                nextEl: '.swiper-button-next-category',
                prevEl: '.swiper-button-prev-category',
              }}
              style={
                {
                  '--swiper-navigation-size': '24px',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                } as CSSProperties
              }
            >
              {categoryData.map(category => (
                <SwiperSlide key={category.id} style={{ height: 'auto' }}>
                  <CategoryCard {...category} />
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>

          {/* Navigation is only rendered when Swiper is ready */}
          {isSwiperReady && (
            <>
              <Flex
                className="swiper-button-prev-category"
                align="center"
                justify="center"
                position="absolute"
                left={0}
                top="35%"
                zIndex={10}
                transform="translateY(-50%)"
                display={{ base: 'none', md: 'flex' }}
                w="44px"
                h="44px"
                borderRadius="full"
                bg="white"
                cursor="pointer"
                boxShadow="md"
                border="1px"
                borderColor="gray.200"
                _hover={{ bg: 'gray.50' }}
              >
                <FiChevronLeft size={24} />
              </Flex>
              <Flex
                className="swiper-button-next-category"
                align="center"
                justify="center"
                position="absolute"
                right={0}
                top="35%"
                zIndex={10}
                transform="translateY(-50%)"
                display={{ base: 'none', md: 'flex' }}
                w="44px"
                h="44px"
                borderRadius="full"
                bg="white"
                cursor="pointer"
                boxShadow="md"
                border="1px"
                borderColor="gray.200"
                _hover={{ bg: 'gray.50' }}
              >
                <FiChevronRight size={24} />
              </Flex>
            </>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default ShopByCategory;
