import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  const { token, cartId } = await request.json();
  const supabase = await createClient();
  const secretKey = process.env.MOYASAR_SECRET_KEY;

  if (!token || !cartId) {
    return NextResponse.json(
      { message: 'Payment token and Cart ID are required.' },
      { status: 400 }
    );
  }
  if (!secretKey) {
    return NextResponse.json(
      { message: 'Server configuration error.' },
      { status: 500 }
    );
  }
  console.log('HERE', cartId);

  try {
    const { data: cart, error: cartError } = await supabase
      .from('cart')
      .select('id, quantity, user_id, circle_details:circle_id(*)')
      .eq('id', cartId)
      .single();

    if (cartError || !cart) {
      console.error(`Cart not found for ID: ${cartId}`, cartError);
      throw new Error('Could not find the specified cart.');
    }

    // 3. Calculate the True Amount on the Server
    // We safely access the sale_price from within the item_details JSONB object.
    const itemDetails = cart.circle_details;
    const salePrice = itemDetails?.sale_price;

    // Add a strict check to ensure the price is a valid number before proceeding.
    if (typeof salePrice !== 'number') {
      throw new Error(`Invalid or missing sale_price for cart ID: ${cartId}`);
    }

    // The `cart.id` and `cart.quantity` properties are now correctly typed and accessible.
    const serverCalculatedAmount = salePrice * cart.quantity!;
    const amountInHalalas = Math.round(serverCalculatedAmount * 100);
    console.log(cart);
    const moyasarResponse = await fetch('https://api.moyasar.com/v1/payments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${Buffer.from(`${secretKey}:`).toString('base64')}`,
      },
      body: JSON.stringify({
        amount: amountInHalalas,
        currency: 'SAR',
        description: `Payment for Cart ID: ${cart.id}`,
        callback_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout?step=status`,
        source: { type: 'token', token: token },
        // 2. Add metadata.
        metadata: {
          cart_id: cart.id,
        },
      }),
    });

    const paymentData = await moyasarResponse.json();
    if (!moyasarResponse.ok) {
      console.error('Moyasar Rejection Details:', paymentData);
      throw new Error(paymentData.message || 'Payment could not be initiated.');
    }

    const { error: updateError } = await supabase.from('order').insert({
      user_id: cart.user_id || null,
      circle_id: cart.circle_details?.id || null,
      quantity: cart.quantity || 1,
      unit_price: salePrice,
      total_price: serverCalculatedAmount,
      status: 'pending',
      payment_method: 'moyasar',
      payment_status: paymentData.status,
    });

    if (updateError) {
      console.error('Failed to link payment ID to cart:', updateError);
      throw new Error('Failed to update cart with payment details.');
    }

    if (paymentData.status === 'initiated') {
      return NextResponse.json({
        transaction_url: paymentData.source.transaction_url,
      });
    } else {
      const statusUrl = new URL(
        `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout?step=status`
      );
      statusUrl.searchParams.set('id', paymentData.id);
      statusUrl.searchParams.set('status', paymentData.status);
      return NextResponse.json({ transaction_url: statusUrl.toString() });
    }
  } catch (error) {
    console.error('Initiate Payment Error:', error);
    return NextResponse.json(
      { message: `${error}` || 'Something went wrong.' },
      { status: 500 }
    );
  }
}
