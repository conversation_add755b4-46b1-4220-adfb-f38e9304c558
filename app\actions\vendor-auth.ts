'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

// Define state types
export type VendorLoginState = {
  error: string | null;
};

export type VendorSignupState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

export type CompanyInfoState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

export type VendorForgotPasswordState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

export type VendorResetPasswordState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

export type VendorEmailVerificationState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

// Removed VendorCodeVerificationState - using Supabase built-in email verification

export async function vendorLogin(
  _prevState: VendorLoginState | undefined,
  formData: FormData
): Promise<VendorLoginState> {
  const supabase = await createClient();

  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    // Check if the error is due to unverified email
    if (
      error.message.includes('email_not_confirmed') ||
      error.message.includes('Email not confirmed')
    ) {
      return {
        error:
          'Please verify your email first. Check your inbox for the verification link.',
      };
    }

    return {
      error: 'Failed to login. Please check email and password and try again.',
    };
  }

  // Get user info to check email verification
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      error: 'Authentication failed. Please try again.',
    };
  }

  // Check if user has a vendor account
  const { data: vendor } = await supabase
    .from('vendor')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (!vendor) {
    return {
      error: 'No vendor account found. Please sign up as a vendor first.',
    };
  }

  // Check if user's email is verified using email_confirmed_at
  if (!user.email_confirmed_at) {
    return {
      error:
        'Please verify your email first. Check your inbox for the verification link.',
    };
  }

  // Check if company info is complete
  if (!vendor.headline || !vendor.description) {
    revalidatePath('/', 'layout');
    redirect('/vendor/auth/company-info');
  }

  revalidatePath('/', 'layout');
  redirect('/vendor/dashboard');
}

export async function vendorSignup(
  _prevState: VendorSignupState | undefined,
  formData: FormData
): Promise<VendorSignupState> {
  const supabase = createServiceRoleClient();

  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;
  const companyName = formData.get('companyName') as string;
  const phone = formData.get('phone') as string;
  const address = formData.get('address') as string;
  const certificateFile = formData.get('certificateFile') as File;

  console.log('Vendor signup attempt:', {
    email,
    companyName,
    hasFile: !!certificateFile,
  });

  // Validation
  if (password !== confirmPassword) {
    return {
      error: 'Passwords do not match',
      success: false,
      message: null,
    };
  }

  if (password.length < 8) {
    return {
      error: 'Password must be at least 8 characters long',
      success: false,
      message: null,
    };
  }

  if (
    !companyName ||
    !phone ||
    !address ||
    !certificateFile ||
    certificateFile.size === 0
  ) {
    return {
      error: 'All fields are required',
      success: false,
      message: null,
    };
  }

  // Validate file size (10MB max)
  if (certificateFile.size > 10 * 1024 * 1024) {
    return {
      error: 'Certificate file must be less than 10MB',
      success: false,
      message: null,
    };
  }

  // Validate file type
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
  ];
  if (!allowedTypes.includes(certificateFile.type)) {
    return {
      error: 'Certificate must be a PDF, JPG, or PNG file',
      success: false,
      message: null,
    };
  }

  // Check if user already exists
  const { data: existingUser } = await supabase
    .from('vendor')
    .select('user_id')
    .eq('contact_email', email)
    .single();

  if (existingUser) {
    return {
      error: 'A vendor account with this email already exists',
      success: false,
      message: null,
    };
  }

  // Create user account
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
  });

  if (authError) {
    console.error('Auth creation error:', authError);
    return {
      error: authError.message,
      success: false,
      message: null,
    };
  }

  if (!authData.user) {
    console.error('No user created');
    return {
      error: 'Failed to create user account',
      success: false,
      message: null,
    };
  }

  console.log('Auth user created:', authData.user.id);

  // Generate vendor ID using database function
  const { data: vendorIdData, error: idError } =
    await supabase.rpc('generate_vendor_id');

  if (idError || !vendorIdData) {
    console.error('Vendor ID generation error:', idError);
    return {
      error: 'Failed to generate vendor ID',
      success: false,
      message: null,
    };
  }

  const vendorId = vendorIdData;

  // Create vendor record with status as 'pending' for email verification
  // Note: slug will be auto-generated by database trigger
  const { data: vendorData, error: vendorError } = await supabase
    .from('vendor')
    .insert({
      id: vendorId,
      slug: 'temp-slug', // Will be overridden by trigger
      user_id: authData.user.id,
      name: companyName,
      contact_email: email,
      contact_phone: phone,
      address: { full_address: address },
      approval_status: 'pending',
      status: 'pending', // Use status field for email verification
    })
    .select('id, slug')
    .single();

  if (vendorError) {
    console.error('Vendor creation error:', vendorError);
    return {
      error: `Failed to create vendor account: ${vendorError.message}`,
      success: false,
      message: null,
    };
  }

  // Use the actual vendor ID returned from database
  const actualVendorId = vendorData.id;
  console.log(
    'Vendor record created successfully:',
    actualVendorId,
    'with slug:',
    vendorData.slug
  );

  // Email verification is handled by Supabase auth system
  console.log(
    'Vendor account created - email verification handled by Supabase auth'
  );

  // Upload certificate file during signup using service role (bypasses RLS)
  try {
    const fileExt = certificateFile.name.split('.').pop();
    const fileName = `certificate.${fileExt}`;
    const filePath = `${actualVendorId}/identity_document/${fileName}`;

    // Use service role client for file upload to bypass storage RLS policies
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('vendor')
      .upload(filePath, certificateFile);

    if (uploadError) {
      console.error('File upload error:', uploadError);
      return {
        error: `Failed to upload certificate: ${uploadError.message}`,
        success: false,
        message: null,
      };
    }

    console.log('File uploaded successfully:', uploadData?.path);

    if (uploadData) {
      // Store document info in vendor_documents table using service role
      const { error: documentError } = await supabase
        .from('vendor_documents')
        .insert({
          vendor_id: actualVendorId,
          document_type: 'identity_document',
          storage_object_id: uploadData.id || uploadData.path,
          is_verified: false,
        });

      if (documentError) {
        console.error('Document storage error:', documentError);
        // Don't fail the signup if document storage fails, just log it
        console.warn(
          'Failed to store document info, but vendor account created successfully'
        );
      }
    }
  } catch (fileError) {
    console.error('File handling error:', fileError);
    return {
      error: `Failed to upload certificate: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`,
      success: false,
      message: null,
    };
  }

  console.log('Vendor signup completed successfully');
  revalidatePath('/', 'layout');

  return {
    error: null,
    success: true,
    message:
      'Check your email for the confirmation link! You will be redirected to login in a few seconds.',
  };
}

export async function updateCompanyInfo(
  _prevState: CompanyInfoState | undefined,
  formData: FormData
): Promise<CompanyInfoState> {
  const supabase = await createClient();

  // Get current user
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      error: 'You must be logged in to complete this action',
      success: false,
      message: null,
    };
  }

  const headline = formData.get('headline') as string;
  const description = formData.get('description') as string;
  const incorporationDate = formData.get('incorporationDate') as string;
  const registrationNumber = formData.get('registrationNumber') as string;
  const bankName = formData.get('bankName') as string;
  const accountNumber = formData.get('accountNumber') as string;
  const iban = formData.get('iban') as string;

  // Validation
  if (
    !headline ||
    !description ||
    !incorporationDate ||
    !registrationNumber ||
    !bankName ||
    !accountNumber ||
    !iban
  ) {
    return {
      error: 'All fields are required',
      success: false,
      message: null,
    };
  }

  // Update vendor record
  const { error } = await supabase
    .from('vendor')
    .update({
      headline,
      description,
      date_of_incorporation: incorporationDate
        ? new Date(incorporationDate).toISOString().split('T')[0]
        : null,
      // Store bank details and registration number in address JSONB field for now
      address: {
        registration_number: registrationNumber,
        bank_name: bankName,
        account_number: accountNumber,
        iban: iban,
      },
      updated_at: new Date().toISOString(),
    })
    .eq('user_id', user.id);

  if (error) {
    return {
      error: 'Failed to update company information. Please try again.',
      success: false,
      message: null,
    };
  }

  revalidatePath('/', 'layout');
  redirect('/vendor/dashboard');
}

export async function vendorForgotPassword(
  _prevState: VendorForgotPasswordState | undefined,
  formData: FormData
): Promise<VendorForgotPasswordState> {
  const supabase = await createClient();

  const email = formData.get('email') as string;

  if (!email) {
    return {
      error: 'Email is required',
      success: false,
      message: null,
    };
  }

  // Check if email belongs to a vendor
  const { data: vendor } = await supabase
    .from('vendor')
    .select('id')
    .eq('contact_email', email)
    .single();

  if (!vendor) {
    return {
      error: 'No vendor account found with this email address',
      success: false,
      message: null,
    };
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/vendor/auth/reset-password`,
  });

  if (error) {
    return {
      error: 'Failed to send reset email. Please try again.',
      success: false,
      message: null,
    };
  }

  return {
    error: null,
    success: true,
    message: 'Password reset link sent to your email address.',
  };
}

export async function vendorResetPassword(
  _prevState: VendorResetPasswordState | undefined,
  formData: FormData
): Promise<VendorResetPasswordState> {
  const supabase = await createClient();

  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  if (password !== confirmPassword) {
    return {
      error: 'Passwords do not match',
      success: false,
      message: null,
    };
  }

  if (password.length < 8) {
    return {
      error: 'Password must be at least 8 characters long',
      success: false,
      message: null,
    };
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    return {
      error: 'Failed to update password. Please try again.',
      success: false,
      message: null,
    };
  }

  return {
    error: null,
    success: true,
    message:
      'Password updated successfully! You can now sign in with your new password.',
  };
}
