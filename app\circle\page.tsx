'use server';
import { Circle, getCircles } from '@/utils/supabase/queries';
import Link from 'next/link';

export default async function CirclesPage() {
  // Fetch circles from the product table
  const circles = await getCircles();

  console.log(circles);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Our Products</h1>

      {circles && circles.length === 0 ? (
        <p>No products found.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {circles?.map((circle: Circle) => (
            <div
              key={circle.id}
              className="border rounded-lg overflow-hidden shadow-md"
            >
              <div className="p-4">
                <h2 className="text-xl font-semibold">{circle.product.name}</h2>
                <p className="text-gray-600 mt-2">{circle.description}</p>
                <div className="mt-4 flex items-center justify-between">
                  <span className="text-xl font-bold">
                    ${circle.sale_price?.toFixed(2)}
                  </span>
                  <Link
                    href={`/product/${circle.product_id}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
