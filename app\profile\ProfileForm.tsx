'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { updateProfile } from './actions';
import {
  Box,
  Button,
  Stack,
  Input,
  VStack,
  Heading,
  Grid,
  GridItem,
  Text,
} from '@chakra-ui/react';
import { Database } from '@/types/supabase';

interface ProfileFormProps {
  profile: Database['public']['Tables']['user_profile']['Row'];
}

export function ProfileForm({ profile: initialProfile }: ProfileFormProps) {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isPending, setIsPending] = useState(false);
  const [profile, setProfile] = useState(initialProfile);
  const router = useRouter();

  async function handleSubmit(formData: FormData) {
    setIsPending(true);
    setError(null);
    setSuccess(null);

    const updates = {
      first_name: formData.get('first_name') as string,
      last_name: formData.get('last_name') as string,
      phone: formData.get('phone') as string,
      address: {
        street: formData.get('street') as string,
        city: formData.get('city') as string,
        state: formData.get('state') as string,
        postal_code: formData.get('postal_code') as string,
        country: formData.get('country') as string,
      },
    };

    const result = await updateProfile(formData);

    if (result.error) {
      setError(result.error);
    } else {
      setSuccess('Profile updated successfully');
      setProfile(prev => ({ ...prev, ...updates }));
      router.refresh();
    }

    setIsPending(false);
  }

  const address =
    (profile.address as {
      street?: string;
      city?: string;
      state?: string;
      postal_code?: string;
      country?: string;
    }) || {};
  return (
    <Box bg="white" shadow="lg" rounded="lg" p={8}>
      <Heading size="lg" mb={8}>
        Profile Settings
      </Heading>

      {error && (
        <Box
          mb={4}
          p={3}
          bg="red.50"
          border="1px"
          borderColor="red.200"
          color="red.700"
          rounded="md"
        >
          {error}
        </Box>
      )}

      {success && (
        <Box
          mb={4}
          p={3}
          bg="green.50"
          border="1px"
          borderColor="green.200"
          color="green.700"
          rounded="md"
        >
          {success}
        </Box>
      )}

      <form action={handleSubmit}>
        <VStack gap={6}>
          <Grid
            templateColumns={{ base: '1fr', sm: 'repeat(2, 1fr)' }}
            gap={6}
            width="100%"
          >
            <GridItem>
              <Stack gap={2}>
                <Text fontWeight="medium">First Name</Text>
                <Input
                  type="text"
                  name="first_name"
                  defaultValue={profile.first_name || ''}
                />
              </Stack>
            </GridItem>

            <GridItem>
              <Stack gap={2}>
                <Text fontWeight="medium">Last Name</Text>
                <Input
                  type="text"
                  name="last_name"
                  defaultValue={profile.last_name || ''}
                />
              </Stack>
            </GridItem>

            <GridItem>
              <Stack gap={2}>
                <Text fontWeight="medium">Phone Number</Text>
                <Input
                  type="tel"
                  name="phone"
                  defaultValue={profile.phone || ''}
                />
              </Stack>
            </GridItem>
          </Grid>

          <Box width="100%">
            <Heading size="md" mb={4}>
              Address
            </Heading>
            <Grid
              templateColumns={{ base: '1fr', sm: 'repeat(2, 1fr)' }}
              gap={6}
            >
              <GridItem colSpan={{ base: 1, sm: 2 }}>
                <Stack gap={2}>
                  <Text fontWeight="medium">Street Address</Text>
                  <Input
                    type="text"
                    name="street"
                    defaultValue={address?.street || ''}
                  />
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={2}>
                  <Text fontWeight="medium">City</Text>
                  <Input
                    type="text"
                    name="city"
                    defaultValue={address?.city || ''}
                  />
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={2}>
                  <Text fontWeight="medium">State / Province</Text>
                  <Input
                    type="text"
                    name="state"
                    defaultValue={address?.state || ''}
                  />
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={2}>
                  <Text fontWeight="medium">Postal Code</Text>
                  <Input
                    type="text"
                    name="postal_code"
                    defaultValue={address?.postal_code || ''}
                  />
                </Stack>
              </GridItem>

              <GridItem>
                <Stack gap={2}>
                  <Text fontWeight="medium">Country</Text>
                  <Input
                    type="text"
                    name="country"
                    defaultValue={address?.country || ''}
                  />
                </Stack>
              </GridItem>
            </Grid>
          </Box>

          <Button
            type="submit"
            colorScheme="blue"
            width="100%"
            isLoading={isPending}
          >
            {isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </VStack>
      </form>
    </Box>
  );
}
