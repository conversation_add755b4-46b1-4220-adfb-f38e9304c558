'use client';
import React from 'react';
import { Flex, Link as ChakraLink, Text, Icon, Box } from '@chakra-ui/react';
import NextLink from 'next/link';
import { FiChevronRight } from 'react-icons/fi';

/*
 * For now since the getCircleById returns product.categories as an array of strings
 * from supabase (see product.categories)
 * We use it as it is.
 *
 * Later we should update the logic and think about product.categories
 * maybe a json like [{category: {parent_category: "", title: ""}}]
 *
 * OR keep it as array
 * Making sure to respect the nested categories when inserting : ["cat", "sub", "sub-sub"]
 *
 * */
interface BreadcrumbsProps {
  items: string[];
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items }) => {
  if (!items || items.length === 0) {
    return null;
  }
  return (
    <Box
      as="nav"
      aria-label="breadcrumb"
      py={4}
      px={{ base: 4, md: 8 }}
      bg="white"
      w="full"
      position="absolute"
      top={0}
      left={0}
      zIndex={10}
    >
      <Flex align="center" wrap="wrap" fontSize="sm" color="gray.600">
        <ChakraLink
          as={NextLink}
          href="/"
          _hover={{ textDecoration: 'underline' }}
        >
          Home
        </ChakraLink>
        <Icon as={FiChevronRight} mx={2} color="gray.400" boxSize={4} />

        {items.map((category, index) => {
          const isLast = index === items.length - 1;

          return (
            <React.Fragment key={category}>
              {isLast ? (
                <Text fontWeight="medium" color="gray.800" aria-current="page">
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Text>
              ) : (
                <ChakraLink
                  as={NextLink}
                  href={`/category/${category}`}
                  _hover={{ textDecoration: 'underline' }}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </ChakraLink>
              )}

              {!isLast && (
                <Icon as={FiChevronRight} mx={2} color="gray.400" boxSize={4} />
              )}
            </React.Fragment>
          );
        })}
      </Flex>
    </Box>
  );
};

export default Breadcrumbs;
