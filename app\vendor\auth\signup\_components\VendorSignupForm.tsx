'use client';

import { useActionState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import {
  vendorSignup,
  type VendorSignupState,
} from '@/app/actions/vendor-auth';
import NextLink from 'next/link';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Link as ChakraLink,
  Spinner,
  Textarea,
  HStack,
  Divider,
} from '@chakra-ui/react';

export default function VendorSignupForm() {
  const formRef = useRef<HTMLFormElement>(null);
  const router = useRouter();
  const toast = useToast();

  const [formState, formAction, isPending] = useActionState<
    VendorSignupState,
    FormData
  >(vendorSignup, { error: null, success: false, message: null });

  useEffect(() => {
    if (formState.error) {
      formRef.current?.reset();
      toast({
        title: 'Error',
        description: formState.error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [formState.error, toast]);

  useEffect(() => {
    if (formState.success && formState.message) {
      toast({
        title: 'Success',
        description: formState.message,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      router.push('/vendor/auth/login');
    }
  }, [formState.success, formState.message, router, toast]);

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="2xl"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Vendor Signup
          </Heading>

          <Text textAlign="center" color="gray.600" fontSize="sm">
            Join our marketplace and start selling to customers
          </Text>

          <form ref={formRef} action={formAction} encType="multipart/form-data">
            <VStack spacing={6}>
              {/* Company Information Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Company Information
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="companyName" color="gray.600">
                      Company Name
                    </FormLabel>
                    <Input
                      id="companyName"
                      name="companyName"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="Your Company Name"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="email" color="gray.600">
                      Business Email
                    </FormLabel>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      autoComplete="email"
                      focusBorderColor="primary"
                      placeholder="<EMAIL>"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="password" color="gray.600">
                      Password
                    </FormLabel>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      autoComplete="new-password"
                      focusBorderColor="primary"
                      placeholder="8+ characters password"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="confirmPassword" color="gray.600">
                      Confirm Password
                    </FormLabel>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      autoComplete="new-password"
                      focusBorderColor="primary"
                      placeholder="Repeat your password"
                      disabled={isPending}
                    />
                  </FormControl>
                </VStack>
              </Box>

              <Divider />

              {/* Contact Information Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Contact Information
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="phone" color="gray.600">
                      Phone Number
                    </FormLabel>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      focusBorderColor="primary"
                      placeholder="+966 50 123 4567"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="address" color="gray.600">
                      Business Address
                    </FormLabel>
                    <Textarea
                      id="address"
                      name="address"
                      focusBorderColor="primary"
                      placeholder="Enter your complete business address"
                      rows={3}
                      disabled={isPending}
                    />
                  </FormControl>
                </VStack>
              </Box>

              <Divider />

              {/* Legal Information Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Legal Information
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="certificateFile" color="gray.600">
                      Certificate of Registration
                    </FormLabel>
                    <Input
                      id="certificateFile"
                      name="certificateFile"
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      focusBorderColor="primary"
                      disabled={isPending}
                      p={1}
                    />
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      Upload PDF, JPG, or PNG file (Max 10MB)
                    </Text>
                  </FormControl>
                </VStack>
              </Box>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Create Vendor Account
              </Button>
            </VStack>
          </form>

          <HStack justify="center" spacing={4}>
            <Text textAlign="center" fontSize="sm" color="gray.600">
              Already have a vendor account?
            </Text>
            <ChakraLink
              as={NextLink}
              href="/vendor/auth/login"
              color="blue.600"
              fontWeight="medium"
              _hover={{ color: 'blue.500' }}
            >
              Sign In
            </ChakraLink>
          </HStack>
        </VStack>
      </Box>
    </Flex>
  );
}
