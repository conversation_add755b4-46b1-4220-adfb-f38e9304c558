'use client';
import { But<PERSON>, <PERSON><PERSON>, <PERSON>, VStack } from '@chakra-ui/react';

export const ShippingAddressPanel = () => {
  return (
    <VStack
      bg="white"
      p={6}
      borderRadius="xl"
      borderWidth="1px"
      borderColor="gray.200"
      align="stretch"
      spacing={4}
    >
      <Heading as="h3" size="md">
        Shipping Address
      </Heading>
      <Button bg="#AFFF02" color="black" _hover={{ bg: '#99e602' }}>
        Add delivery address
      </Button>
      <Link
        color="blue.500"
        fontSize="sm"
        textAlign="center"
        _hover={{ textDecoration: 'underline' }}
      >
        Find a pickup location nearby
      </Link>
    </VStack>
  );
};
