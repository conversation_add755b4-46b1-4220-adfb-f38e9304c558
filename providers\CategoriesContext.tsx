'use client';
import { createContext, useContext, useEffect, useState } from 'react';
import { getCategories, Category } from '@/utils/supabase/queries';

const CategoriesContext = createContext<{ categories: Category[] }>({
  categories: [],
});

export function CategoriesProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    (async () => {
      const cats = await getCategories();
      setCategories(cats);
    })();
  }, []);

  return (
    <CategoriesContext.Provider
      value={{
        categories,
      }}
    >
      {children}
    </CategoriesContext.Provider>
  );
}

export const useCategories = () => {
  const context = useContext(CategoriesContext);
  if (!context)
    throw new Error('useCategories must be used within a CategoriesProvider');
  return context;
};
