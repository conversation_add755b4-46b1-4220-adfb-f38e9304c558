'use client';
import React, { useState, useEffect, useRef } from 'react';
import {
  Input,
  InputGroup,
  InputLeftElement,
  Box,
  VStack,
  Text,
  Link,
  Spinner,
  Flex,
  useOutsideClick,
  Badge,
  Avatar,
  BoxProps,
} from '@chakra-ui/react';
import { FiSearch } from 'react-icons/fi';
import NextLink from 'next/link';

// Define types based on the API response
interface Contact {
  name: string;
  affiliation: string;
}

interface DataverseResponse {
  status: string;
  data?: {
    q: string;
    total_count: number;
    start: number;
    spelling_alternatives: Record<string, string>;
    items: Item[];
    count_in_response: number;
  };
}

type ItemType = 'dataverse' | 'file' | 'dataset';

interface BaseItem {
  name: string;
  type: ItemType;
  url: string;
  image_url?: string;
  description?: string;
  published_at: string;
  publicationStatuses: string[];
}

interface DataverseItem extends BaseItem {
  type: 'dataverse';
  identifier: string;
  affiliation: string;
  parentDataverseName: string;
  parentDataverseIdentifier: string;
}

interface FileItem extends BaseItem {
  type: 'file';
  file_id: string;
  file_type: string;
  file_content_type: string;
  size_in_bytes: number;
  md5: string;
  file_persistent_id: string;
  dataset_name: string;
  dataset_id: string;
  dataset_persistent_id: string;
  dataset_citation: string;
  releaseOrCreateDate: string;
}

interface DatasetItem extends BaseItem {
  type: 'dataset';
  global_id: string;
  publisher: string;
  citationHtml: string;
  identifier_of_dataverse: string;
  name_of_dataverse: string;
  citation: string;
  storageIdentifier: string;
  subjects: string[];
  fileCount: number;
  versionId: number;
  versionState: string;
  majorVersion: number;
  minorVersion: number;
  createdAt: string;
  updatedAt: string;
  contacts: Contact[];
  producers: string[];
  authors: string[];
}

type Item = DataverseItem | FileItem | DatasetItem;

const SearchBox = ({
  display,
  bg = 'white',
  borderRadius = 'md',
}: {
  display?: BoxProps['display'];
  bg?: string;
  borderRadius?: string;
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useOutsideClick({
    ref,
    handler: () => setIsFocused(false),
  });

  useEffect(() => {
    if (query.trim() === '') {
      setResults([]);
      return;
    }
    /**
     *  For now we use dataverse api to demo the seach feature
     *  later when we build a search api endpoint we will update
     *  the component
     *
     */
    const fetchResults = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(
          `https://demo.dataverse.org/api/search?q=${encodeURIComponent(query)}`
        );
        const data: DataverseResponse = await response.json();

        if (data.status === 'OK' && data.data?.items) {
          setResults(data.data.items.slice(0, 5));
        } else {
          setResults([]);
        }
      } catch (error) {
        console.error('Search failed:', error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    const handler = setTimeout(fetchResults, 300);
    return () => clearTimeout(handler);
  }, [query]);

  const showResults = isFocused && query.trim() !== '';

  const renderResultItem = (item: Item) => {
    const id =
      item.type === 'dataverse'
        ? item.identifier
        : item.type === 'file'
          ? item.file_id
          : item.global_id;

    return (
      <Link
        key={id}
        as={NextLink}
        href={item.url}
        p={3}
        display="flex"
        alignItems="flex-start"
        gap={3}
        _hover={{ bg: 'gray.50' }}
        borderBottomWidth="1px"
        borderColor="gray.100"
        onClick={() => {
          setQuery('');
          setIsFocused(false);
        }}
      >
        {item.image_url && (
          <Avatar
            src={item.image_url}
            name={item.name}
            size="sm"
            bg="gray.200"
          />
        )}

        <Box flex="1">
          <Flex align="center" gap={2} mb={1}>
            <Text fontWeight="medium" noOfLines={1}>
              {item.name}
            </Text>
            <Badge
              colorScheme={
                item.type === 'dataset'
                  ? 'blue'
                  : item.type === 'file'
                    ? 'green'
                    : 'purple' // dataverse
              }
              fontSize="xs"
            >
              {item.type}
            </Badge>
          </Flex>

          {item.description && (
            <Text fontSize="sm" color="gray.600" noOfLines={1}>
              {item.description}
            </Text>
          )}

          <Text fontSize="xs" color="gray.500" mt={1}>
            Published: {new Date(item.published_at).toLocaleDateString()}
          </Text>

          {item.type === 'file' && (
            <Text fontSize="xs" color="gray.500">
              Type: {item.file_type} • Size:{' '}
              {formatFileSize(item.size_in_bytes)}
            </Text>
          )}
        </Box>
      </Link>
    );
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box ref={ref} position="relative" w="full" display={display}>
      <InputGroup borderRadius={borderRadius}>
        <InputLeftElement pointerEvents="none">
          <FiSearch color="gray" />
        </InputLeftElement>
        <Input
          placeholder="Search Product"
          bg={bg}
          color="black"
          value={query}
          onChange={e => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          _placeholder={{ color: 'gray.500' }}
        />
      </InputGroup>

      {showResults && (
        <Box
          position="absolute"
          top="100%"
          left="0"
          right="0"
          mt={1}
          bg="white"
          boxShadow="xl"
          borderRadius="md"
          zIndex="dropdown"
          maxH="400px"
          overflowY="auto"
          borderWidth="1px"
          borderColor="gray.200"
        >
          {isLoading ? (
            <Flex justify="center" py={4}>
              <Spinner />
            </Flex>
          ) : results.length > 0 ? (
            <VStack align="stretch" spacing={0}>
              {results.map(renderResultItem)}
            </VStack>
          ) : (
            <Text p={3} color="gray.500" textAlign="center">
              No results found for &quot;{query}&quot;
            </Text>
          )}
        </Box>
      )}
    </Box>
  );
};

export default SearchBox;
