'use client';

import { useActionState, useRef, useEffect } from 'react';
import { login } from '@/app/actions/auth';
import NextLink from 'next/link';
import { useRouter } from 'next/navigation';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Link as ChakraLink,
  Spinner,
} from '@chakra-ui/react';

export default function LoginForm() {
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);

  // Use the new useActionState hook
  const [formState, formAction, isPending] = useActionState(login, {
    error: null,
  });

  useEffect(() => {
    if (formState.error) {
      // Reset the form after showing error
      formRef.current?.reset();
    }
  }, [formState, router]);

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="md"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Welcome back
          </Heading>

          <form ref={formRef} action={formAction}>
            <VStack spacing={4}>
              {formState.error && (
                <Box
                  bg="red.50"
                  borderRadius="md"
                  p={3}
                  borderWidth={1}
                  borderColor="red.200"
                >
                  <Text color="red.700" fontWeight="medium" fontSize="sm">
                    {formState.error}
                  </Text>
                </Box>
              )}
              <FormControl isRequired>
                <FormLabel htmlFor="email" color="gray.600">
                  Email address
                </FormLabel>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  focusBorderColor="primary"
                  disabled={isPending}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel htmlFor="password" color="gray.600">
                  Password
                </FormLabel>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  focusBorderColor="primary"
                  disabled={isPending}
                />
                <Flex justify="flex-end" mt={2}>
                  <ChakraLink
                    as={NextLink}
                    href="/auth/forgot-password"
                    fontSize="sm"
                    color="blue.600"
                    fontWeight="medium"
                    _hover={{ color: 'blue.500' }}
                  >
                    Forgot password?
                  </ChakraLink>
                </Flex>
              </FormControl>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Sign In
              </Button>
            </VStack>
          </form>

          <Text textAlign="center">
            <ChakraLink
              as={NextLink}
              href="/auth/signup"
              color="blue.600"
              fontWeight="medium"
              _hover={{ color: 'blue.500' }}
            >
              Do not have an account? Sign up
            </ChakraLink>
          </Text>
        </VStack>
      </Box>
    </Flex>
  );
}
