'use client';
import { useEffect } from 'react';
import { useToast } from '@chakra-ui/react';

// Define the global Moyasar type
declare global {
  interface Window {
    Moyasar: {
      init: (config: unknown) => void;
    };
  }
}

type MoyasarDropInFormProps = {
  apiKey: string;
  amount: number; // In smallest currency unit
  callbackUrl: string;
};

export const MoyasarDropInForm = ({
  apiKey,
  amount,
  callbackUrl,
}: MoyasarDropInFormProps) => {
  const toast = useToast();

  // This function is the implementation of "savePaymentOnBackend"
  const handlePaymentCompleted = async (payment: JSON) => {
    try {
      const response = await fetch('/api/payment/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payment),
      });

      if (!response.ok) {
        throw new Error('Failed to save payment details.');
      }

      console.log('Payment ID saved successfully before redirect.');
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description:
          'Could not save payment details. Please contact support if you are charged.',
        status: 'error',
        duration: 10000,
      });
    }
  };

  useEffect(() => {
    // Wait for the Moyasar script (loaded in layout.tsx) to be ready
    if (window.Moyasar) {
      window.Moyasar.init({
        element: '.mysr-form',
        amount: amount,
        currency: 'SAR',
        description: 'Order payment for Collab',
        publishable_api_key: apiKey,
        callback_url: callbackUrl,
        supported_networks: ['mada', 'visa', 'mastercard', 'amex'],
        methods: ['creditcard'],
        on_completed: handlePaymentCompleted,
      });
    } else {
      console.error('Moyasar script not loaded.');
    }
  }, [apiKey, amount, callbackUrl, toast]); // Re-run if props change

  return <div className="mysr-form"></div>;
};
