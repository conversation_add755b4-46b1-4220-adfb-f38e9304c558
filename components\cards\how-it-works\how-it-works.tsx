import { Box, Image, Heading, Text, VStack } from '@chakra-ui/react';
import type { ReactNode } from 'react';

interface HowItWorksCardProps {
  imageSrc: string;
  title: ReactNode;
  description: string;
}

export const HowItWorksCard = ({
  imageSrc,
  title,
  description,
}: HowItWorksCardProps) => {
  return (
    <Box
      bg="white"
      borderRadius="xl"
      overflow="hidden"
      boxShadow="md"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-5px)',
        boxShadow: 'lg',
      }}
    >
      <Image
        src={imageSrc}
        alt={typeof title === 'string' ? title : 'How Collab Works Step'}
        objectFit="cover"
        w="full"
        borderRadius="xl"
        h={{ base: '180px', md: '160px' }} // Set a consistent height for the images
      />
      <VStack
        p={{ base: 3, md: 6 }}
        spacing={2}
        align={{ base: 'center', md: 'start' }}
      >
        <Heading as="h3" size={{ base: 'sm', md: 'lg' }} color="gray.900">
          {title}
        </Heading>
        <Text color="gray.600" fontSize={{ base: 'xs', md: 'sm' }}>
          {description}
        </Text>
      </VStack>
    </Box>
  );
};
