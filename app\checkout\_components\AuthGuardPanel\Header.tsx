'use client';
import { Flex, Text, IconButton } from '@chakra-ui/react';
import { FiArrowLeft } from 'react-icons/fi';

type HeaderProps = {
  onBack: () => void;
};

const Header = ({ onBack }: HeaderProps) => {
  return (
    <Flex
      as="header"
      bg="#BFFF40"
      p={4}
      align="center"
      justify="space-between"
      w="full"
    >
      <Flex align="center">
        <IconButton
          icon={<FiArrowLeft />}
          aria-label="Back"
          onClick={onBack}
          variant="ghost"
          fontSize="xl"
          mr={2}
        />
        <Text fontWeight="medium" display={{ base: 'none', md: 'block' }}>
          Order
        </Text>
      </Flex>
      <Text fontWeight="bold">Secure Checkout</Text>
    </Flex>
  );
};

export { Header };
