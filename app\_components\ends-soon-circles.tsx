'use client';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>r,
  <PERSON><PERSON>,
  <PERSON>ton,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@chakra-ui/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import { CircleCard } from '@/components/cards';
import { <PERSON><PERSON><PERSON>, FiTrendingUp } from 'react-icons/fi';
import { useState } from 'react';
import { Circle } from '@/utils/supabase/queries';

interface EndsSoonCirclesProps {
  circles: Circle[];
}

const EndsSoonCircles = ({ circles }: EndsSoonCirclesProps) => {
  const [isSwiperReady, setIsSwiperReady] = useState(false);

  if (!circles || circles.length === 0) {
    return null;
  }

  return (
    <Box bg="#F9FAFB" p={{ base: 4, md: 8 }}>
      <Box
        as="section"
        bg="white"
        maxW="container.xl"
        mx="auto"
        borderRadius="xl"
        boxShadow="base"
        overflow="hidden"
        py={6}
        px={{ base: 2, sm: 4, md: 6 }}
      >
        <Flex align="center" mb={6} px={{ base: 4, sm: 0 }}>
          <Heading
            as="h2"
            fontSize={{ base: 'xl', md: '2xl' }}
            fontWeight="bold"
          >
            Ends Soon – Flash Circles
          </Heading>
          <Spacer />
          <HStack spacing={2} display={{ base: 'none', md: 'flex' }}>
            <Button
              bg="orange.100"
              color="orange.600"
              size="sm"
              borderRadius="full"
              leftIcon={<Icon as={FiClock} color="orange.500" w={4} h={4} />}
              _hover={{ bg: 'orange.200' }}
            >
              Ending Soon
            </Button>
            <Button
              variant="outline"
              borderColor="gray.200"
              color="gray.600"
              size="sm"
              borderRadius="full"
              leftIcon={<Icon as={FiTrendingUp} w={4} h={4} />}
              _hover={{ bg: 'gray.50' }}
            >
              Trending
            </Button>
          </HStack>
        </Flex>

        <Box position="relative" pl={{ base: 4, sm: 4, md: 6 }} minH="full">
          {!isSwiperReady && (
            <Flex
              align="center"
              justify="center"
              h="450px"
              position="absolute"
              w="full"
              zIndex={10}
            >
              <Spinner
                size="xl"
                thickness="4px"
                speed="0.65s"
                emptyColor="gray.200"
                color="primary"
              />
            </Flex>
          )}

          <Box
            opacity={isSwiperReady ? 1 : 0}
            transition="opacity 0.4s ease-in-out"
          >
            <Swiper
              onSwiper={() => setIsSwiperReady(true)}
              spaceBetween={16}
              slidesPerView={'auto'}
              breakpoints={{
                320: { slidesPerView: 1.15, spaceBetween: 12 },
                640: { slidesPerView: 2.2, spaceBetween: 16 },
                768: { slidesPerView: 2.5, spaceBetween: 16 },
                1024: { slidesPerView: 3.5, spaceBetween: 20 },
                1280: { slidesPerView: 4, spaceBetween: 20 },
              }}
            >
              {circles.map(circle => (
                <SwiperSlide key={circle.id} style={{ height: 'auto' }}>
                  <CircleCard circle={circle} />
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default EndsSoonCircles;
