'use client';
import React from 'react';
import {
  Box,
  Text,
  Drawer,
  DrawerOverlay,
  Drawer<PERSON>ontent,
  DrawerHeader,
  DrawerBody,
  DrawerCloseButton,
  Stack,
  Divider,
  But<PERSON>,
} from '@chakra-ui/react';
import { FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { Category } from '@/utils/supabase/queries';
const MobileDrawer = ({
  isOpen,
  onClose,
  drawerView,
  selectedCategory,
  categories,
  openSubcategory,
  goBackToMain,
  handleSubcategoryClick,
}: {
  isOpen: boolean;
  onClose: () => void;
  drawerView: 'main' | 'sub' | null;
  selectedCategory: Category | null;
  categories: Category[];
  openSubcategory: (category: Category) => void;
  goBackToMain: () => void;
  handleSubcategoryClick: (categoryId: string, subcategoryId: string) => void;
}) => {
  return (
    <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton size="lg" color="gray.600" />
        <DrawerHeader borderBottomWidth="1px" py={4}>
          <Text fontSize="xl" fontWeight="bold">
            Collab
          </Text>
        </DrawerHeader>

        <DrawerBody py={4} px={0}>
          <Box py={3} px={6}>
            {!selectedCategory && (
              <Text fontSize={{ base: 'lg', md: 'xl' }} fontWeight="semibold">
                Categories
              </Text>
            )}
          </Box>

          {drawerView === 'main' && (
            <Stack spacing={0}>
              {categories.map(category => (
                <React.Fragment key={category.id}>
                  <Button
                    variant="ghost"
                    justifyContent="space-between"
                    borderRadius={0}
                    py={4}
                    px={6}
                    onClick={() => openSubcategory(category)}
                    rightIcon={<FiChevronRight />}
                    textAlign="left"
                    color="black"
                    _hover={{ bg: 'gray.50' }}
                  >
                    {category.name}
                  </Button>
                  <Divider borderColor="gray.200" />
                </React.Fragment>
              ))}
            </Stack>
          )}

          {drawerView === 'sub' && selectedCategory && (
            <Stack spacing={4}>
              <Button
                variant="ghost"
                leftIcon={<FiChevronLeft />}
                justifyContent="flex-start"
                pl={4}
                onClick={goBackToMain}
                _hover={{ bg: 'gray.50' }}
              >
                {selectedCategory.name}
              </Button>

              <Stack spacing={1} px={4}>
                {selectedCategory.subcategories?.map((sub: Category) => (
                  <Button
                    key={sub.id}
                    variant="ghost"
                    justifyContent="flex-start"
                    pl={6}
                    py={3}
                    color="gray.700"
                    fontWeight="normal"
                    textAlign="left"
                    _hover={{ bg: 'gray.50' }}
                    onClick={() => {
                      handleSubcategoryClick(selectedCategory.slug, sub.slug);
                    }}
                  >
                    {sub.name}
                  </Button>
                ))}
              </Stack>
            </Stack>
          )}
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileDrawer;
