'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/providers/CartContext';
import {
  Box,
  Grid,
  GridItem,
  VStack,
  HStack,
  Flex,
  Heading,
  Text,
  Button,
  Icon,
  Badge,
  CircularProgress,
  CircularProgressLabel,
  Avatar,
  AvatarGroup,
  Divider,
  IconButton,
} from '@chakra-ui/react';
import { FiHeart, FiShare2, FiClock } from 'react-icons/fi';
import {
  FaTag,
  FaTags,
  FaCalendarAlt,
  FaShieldAlt,
  FaStar,
} from 'react-icons/fa';
import { CustomBadge } from '@/components/cards/circle/circle-card';
import { ProductImageGallery } from './images-gallery';
import { Circle, PricingTier } from '@/utils/supabase/queries';
import { getPrice } from '@/utils/lib';

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

const formatTimeRemaining = (endDateString: string): string => {
  const now = new Date();
  const endDate = new Date(endDateString);
  const diff = endDate.getTime() - now.getTime();
  if (diff <= 0) return 'Ended';
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  let result = '';
  if (days > 0) result += `${days} day${days !== 1 ? 's' : ''} `;
  if (hours > 0 || days === 0)
    result += `${hours} hour${hours !== 1 ? 's' : ''}`;
  return result.trim();
};

const InfoCard = ({
  icon,
  title,
  children,
}: {
  icon: React.ElementType;
  title: string;
  children: React.ReactNode;
}) => (
  <VStack
    align="stretch"
    spacing={3}
    p={4}
    bg="gray.50"
    borderRadius="lg"
    borderWidth="1px"
    borderColor="gray.200"
  >
    <HStack spacing={3}>
      <Icon as={icon} color="orange.500" />
      <Heading as="h3" fontSize="sm" fontWeight="bold">
        {title}
      </Heading>
    </HStack>
    <VStack align="stretch" spacing={2}>
      {children}
    </VStack>
  </VStack>
);
const DetailItem = ({
  label,
  value,
  isBold = false,
}: {
  label: string;
  value: React.ReactNode;
  isBold?: boolean;
}) => (
  <Flex justify="space-between" align="center">
    <Text fontSize="sm" color="gray.600">
      {label}
    </Text>
    <Text
      fontSize="sm"
      color="gray.900"
      fontWeight={isBold ? 'bold' : 'medium'}
    >
      {value}
    </Text>
  </Flex>
);

interface CircleDetailsProps {
  circle: Circle;
}

const CircleDetails: React.FC<CircleDetailsProps> = ({ circle }) => {
  const router = useRouter();
  const { addItem } = useCart();
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [timerText, setTimerText] = useState(
    formatTimeRemaining(circle.end_date)
  );

  useEffect(() => {
    const intervalId = setInterval(
      () => setTimerText(formatTimeRemaining(circle.end_date)),
      60000
    );
    return () => clearInterval(intervalId);
  }, [circle.end_date]);

  const {
    description,
    sale_price,
    current_participants,
    max_participants,
    end_date,
    start_date,
  } = circle;
  const pricing_tiers = (circle.pricing_tiers as PricingTier[]) ?? [];
  const { market_price } = circle.product || {};
  const progressPercentage =
    max_participants > 0
      ? Math.round((current_participants! / max_participants) * 100)
      : 0;
  const { price, nextTier } = getPrice(
    pricing_tiers,
    sale_price,
    Number(current_participants)
  );
  const sortedTiers = [...pricing_tiers].sort(
    (a, b) => (a.threshold || 0) - (b.threshold || 0)
  );

  const savings = market_price ? market_price - Number(price) : 0;
  const savingsPercentage = market_price
    ? Math.round((savings / market_price) * 100)
    : 0;

  const nextTierPriceChangePercentage =
    nextTier && Number(price) > 0
      ? Math.round(
          nextTier ? ((nextTier.price - sale_price) / sale_price) * 100 : 10
        )
      : Math.round((savings / (market_price ?? Number(price))) * 100);
  //console.log(nextTier?.price)
  const imageUrls = circle?.product?.images || [];
  const endDateObj = new Date(end_date);
  const shippingDate = new Date(endDateObj);
  shippingDate.setDate(shippingDate.getDate() + 6);

  const isButtonDisabled = new Date(end_date) <= new Date();
  const supplier = circle.vendor || null;
  const handleJoinCircle = () => {
    setIsAddingToCart(true);
    addItem(circle, 1);
    router.push('/checkout');
  };

  return (
    <Box
      maxW="container.xl"
      mx="auto"
      p={{ base: 4, md: 6, lg: 8 }}
      bg="white"
      mt="10"
    >
      <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 6, lg: 8 }}>
        {/* === COLUMN 1: Image Gallery & Call to Action === */}
        <GridItem colSpan={{ base: 12, lg: 5 }}>
          <VStack align="stretch" spacing={4} position="sticky" top={8}>
            <Box position="relative">
              <ProductImageGallery
                images={imageUrls}
                alt={description as string}
              />
              <HStack position="absolute" top={4} right={4} zIndex={10}>
                <IconButton
                  aria-label="Add to wishlist"
                  icon={<FiHeart />}
                  isRound
                  variant="outline"
                  bg="white"
                  boxShadow="sm"
                />
                <IconButton
                  aria-label="Share"
                  icon={<FiShare2 />}
                  isRound
                  variant="outline"
                  bg="white"
                  boxShadow="sm"
                />
              </HStack>
            </Box>

            <Button
              colorScheme="green"
              bgColor="#AFFF02"
              color="black"
              size="lg"
              h="56px"
              fontSize="xl"
              fontWeight="bold"
              _hover={{ bgColor: '#99e602' }}
              onClick={handleJoinCircle}
              isLoading={isAddingToCart}
              isDisabled={isButtonDisabled}
              loadingText="Adding to Cart..."
            >
              {isButtonDisabled
                ? 'Circle Ended'
                : savingsPercentage > 0
                  ? `Join Now and Save ${nextTierPriceChangePercentage}%`
                  : 'Join Now'}
            </Button>
          </VStack>
        </GridItem>

        {/* === COLUMN 2 & 3: Details === */}
        <GridItem colSpan={{ base: 12, lg: 4 }}>
          <VStack align="stretch" spacing={6}>
            <VStack align="flex-start" spacing={3}>
              <CustomBadge> Sale Circle </CustomBadge>
              <Heading as="h1" size="md" fontWeight="bold">
                {description}
              </Heading>
              {supplier && (
                <HStack>
                  <Text fontSize="sm" fontWeight="medium">
                    {supplier.name}
                  </Text>
                  <HStack color="orange.400" spacing={0.5}>
                    <FaStar />
                  </HStack>
                </HStack>
              )}
              <HStack>
                <Icon as={FiClock} />
                <Text fontSize="sm">Ends in : {timerText}</Text>
              </HStack>
              <HStack align="baseline" spacing={3}>
                <Text
                  fontSize={{ base: '2xl', md: '3xl' }}
                  fontWeight="bold"
                  color="gray.900"
                >
                  <Text
                    as="span"
                    fontSize="2xl"
                    verticalAlign="middle"
                    mr={1}
                  >{`﷼`}</Text>
                  {`  ${price}`}
                </Text>
                {market_price && (
                  <Text
                    as="s"
                    fontSize="xl"
                    color="gray.500"
                  >{`﷼ ${market_price.toFixed(2)}`}</Text>
                )}
              </HStack>

              <HStack fontSize="sm" spacing={4} align="center">
                {savings > 0 && (
                  <Text color="gray.600">
                    Saving:{' '}
                    <Text as="span" fontWeight="bold" color="gray.800">
                      {`﷼ ${savings.toFixed(2)}`}
                    </Text>
                  </Text>
                )}
                {nextTier && (
                  <HStack align="center" spacing={2}>
                    <Text color="gray.600" whiteSpace="nowrap">
                      Next Tier:{' '}
                      <Text as="span" fontWeight="bold" color="gray.800">
                        {`﷼${nextTier.price.toFixed(2)}`}
                      </Text>
                    </Text>
                    {nextTierPriceChangePercentage > 0 && (
                      <Badge
                        colorScheme="orange"
                        variant="solid"
                        px={2}
                        py={0.5}
                        borderRadius="md"
                        fontSize="0.7em"
                      >
                        <HStack spacing={0.5} align="center">
                          <Text>{nextTierPriceChangePercentage}%</Text>
                          <Text
                            as="span"
                            lineHeight={1}
                            style={{ transform: 'translateY(-1px)' }}
                          >
                            ↗
                          </Text>
                        </HStack>
                      </Badge>
                    )}
                  </HStack>
                )}
              </HStack>
              {/* --- END OF REVERT --- */}
            </VStack>
            <Divider />
            <VStack spacing={4}>
              <CircularProgress
                value={progressPercentage}
                size="200px"
                thickness="8px"
                color="green.400"
                trackColor="gray.200"
              >
                <CircularProgressLabel>
                  <VStack>
                    <Text fontSize="4xl" fontWeight="bold" color="gray.900">
                      {progressPercentage}%
                    </Text>
                    <Text fontSize="md" color="gray.600" mt={-2}>
                      {current_participants} joined
                    </Text>
                  </VStack>
                </CircularProgressLabel>
              </CircularProgress>
              <AvatarGroup size="sm" max={3} spacing={-2}>
                {/* for now use simple array.from to display 
                 // exact number of avatars based on Current_participant 
                 // maybe later update this to fetch paritcipant based on user_id 
                 // from joined table (current_participants)
                */}
                {Array.from({ length: current_participants! }).map(
                  (_, index) => (
                    <Avatar key={index} name={`Participant ${index + 1}`} />
                  )
                )}
              </AvatarGroup>
            </VStack>
          </VStack>
        </GridItem>
        <GridItem colSpan={{ base: 12, lg: 3 }}>
          <VStack align="stretch" spacing={4}>
            <Heading
              as="h2"
              size="sm"
              textTransform="uppercase"
              letterSpacing="wider"
            >
              Circle Details
            </Heading>
            <InfoCard icon={FaTags} title="Current Tier">
              <DetailItem
                label="Current tier"
                value={`﷼ ${price.toFixed(2)}`}
                isBold
              />
            </InfoCard>
            <InfoCard icon={FaTag} title="Pricing Tiers">
              {/* Use the pre-sorted 'sortedTiers' array for guaranteed logical consistency */}
              {sortedTiers.map((tier, index, tiers) => {
                // Determine the start of the range for the label.
                // For the first tier (index 0), the range starts at 1.
                // For subsequent tiers, it starts 1 participant after the previous tier's threshold.
                const startOfRange =
                  index > 0 ? tiers[index - 1].threshold! + 1 : 1;

                return (
                  <DetailItem
                    key={tier.price}
                    label={`${startOfRange}-${tier.threshold} Buyers`}
                    value={`﷼ ${tier.price.toFixed(2)}`}
                  />
                );
              })}
            </InfoCard>
            <InfoCard icon={FaCalendarAlt} title="Timeline">
              <DetailItem label="Start date" value={formatDate(start_date)} />
              <DetailItem label="End date" value={formatDate(end_date)} />
              <DetailItem
                label="Estimated Shipping"
                value={formatDate(shippingDate.toISOString())}
              />
            </InfoCard>
            <InfoCard icon={FaShieldAlt} title="Refund Policy">
              <Text fontSize="sm" color="gray.600">
                Full refund if circle fails to reach {max_participants}{' '}
                participants
              </Text>
            </InfoCard>
            {supplier && (
              <>
                <Heading
                  as="h2"
                  size="sm"
                  textTransform="uppercase"
                  letterSpacing="wider"
                  pt={4}
                >
                  Supplier Information
                </Heading>
                <VStack
                  align="stretch"
                  p={4}
                  bg="gray.50"
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor="gray.200"
                >
                  <DetailItem label="Supplier Name" value={supplier.name} />
                </VStack>
              </>
            )}
          </VStack>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default CircleDetails;
