'use client';
import React, { CSSProperties } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  Divider,
  Stack,
} from '@chakra-ui/react';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

/**
 * For now HeroSection data is hard coded
 * later we will update it to use data from supabase
 * maybe filtering sale_circle to show 5 circles
 * */
const HeroSection = () => {
  const slides = [
    {
      title: 'NEW COLLECTION',
      description:
        'Join the community-powered buying circle and unlock bulk-pricing savings instantly.',
      collection: 'Dubly',
      prices: ['$1,000', '$0.00', '$1,000'],
      bg: 'linear-gradient(135deg, #3182CE 0%, #63B3ED 100%)',
    },
    {
      title: 'SUMMER ESSENTIALS',
      description:
        'Discover our curated selection of seasonal must-haves at unbeatable prices.',
      collection: 'Solstice',
      prices: ['$899', '$49.99', '$799'],
      bg: 'linear-gradient(135deg, #38B2AC 0%, #81E6D9 100%)',
    },
    {
      title: 'PREMIUM SELECTION',
      description:
        'Experience luxury without compromise with our handcrafted premium line.',
      collection: 'Aurora',
      prices: ['$2,499', '$199.99', '$2,299'],
      bg: 'linear-gradient(135deg, #805AD5 0%, #B794F4 100%)',
    },
    {
      title: 'PREMIUM SELECTION',
      description:
        'Experience luxury without compromise with our handcrafted premium line.',
      collection: 'Aurora',
      prices: ['$2,499', '$199.99', '$2,299'],
      bg: 'linear-gradient(135deg, #805AD5 0%, #B794F4 100%)',
    },
  ];

  return (
    <Box
      w="full"
      position="relative"
      overflow="hidden"
      mx={{ base: 'auto', md: 'none' }}
      maxW={{ base: '95%', md: 'full' }}
      borderRadius={{ base: 'xl', md: 'none' }}
      sx={{
        // Custom pagination bullet styles
        '.swiper-pagination-bullet': {
          width: '35px !important',
          height: '4px !important',
          borderRadius: '2px !important',
          background: 'rgba(255,255,255,0.5) !important',
          opacity: '1 !important',
          transition: 'all 0.3s ease !important',
        },
        '.swiper-pagination-bullet-active': {
          width: '150px !important',
          background: 'white !important',
        },
      }}
    >
      <Swiper
        modules={[Autoplay, Pagination, Navigation]}
        spaceBetween={0}
        slidesPerView={1}
        autoplay={{ delay: 5000, disableOnInteraction: false }}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet',
          bulletActiveClass: 'swiper-pagination-bullet-active',
        }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }}
        loop={true}
        style={
          {
            '--swiper-pagination-color': '#3182CE',
            '--swiper-pagination-bullet-size': '20px',
            '--swiper-navigation-size': '20px',
            '--swiper-pagination-bottom': '20px',
            '--swiper-pagination-bullet-horizontal-gap': '7px',
            height: '25rem',
          } as CSSProperties
        }
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <Flex
              direction={{ base: 'column', md: 'row' }}
              h="full"
              bg={slide.bg}
              color="white"
              px={{ base: 5, md: 12, lg: 20 }}
              py={{ base: 5, md: 7 }}
              gap={5}
              justify="space-between"
            >
              {/* Text Content */}
              <Flex
                direction={['column-reverse', 'row']}
                justify="center"
                align="center"
                w="full"
                pr={{ md: 4 }}
                textAlign={{ base: 'center', md: 'left' }}
                mx={{ base: 'none', md: '3rem' }}
              >
                <Box>
                  <Heading
                    as="h1"
                    fontSize={{ base: 'xl', md: '2xl', lg: '6xl' }}
                    fontWeight="bold"
                    mb={2}
                  >
                    {slide.title}
                  </Heading>

                  <Text
                    fontSize={{ base: 'sm', md: 'md' }}
                    mb={4}
                    maxW={{ md: '90%' }}
                  >
                    {slide.description}
                  </Text>

                  {/* Compact info section */}
                  <Flex
                    direction={{ base: 'column', md: 'row' }}
                    justify={{ base: 'center', md: 'flex-start' }}
                    align="center"
                    gap={{ base: 2, md: 4 }}
                    mb={3}
                  >
                    <Box display={{ base: 'none', md: 'block' }}>
                      <Text fontSize="xs" opacity={0.9} mb={0.5}>
                        COLLECTION
                      </Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {slide.collection}
                      </Text>
                    </Box>

                    <Box display={{ base: 'none', md: 'block' }}>
                      <Text fontSize="xs" opacity={0.9} mb={0.5}>
                        Cold Prices
                      </Text>
                      <Stack direction="row" spacing={2}>
                        {slide.prices.map((price, i) => (
                          <Text key={i} fontSize="xs">
                            {price}
                          </Text>
                        ))}
                      </Stack>
                    </Box>
                  </Flex>
                  <Button
                    colorScheme="whiteAlpha"
                    variant="outline"
                    size={{ base: 'sm', md: 'lg' }}
                    w={{ base: 'full', md: 'auto' }}
                    minW={{ md: '50%' }}
                    mx={{ base: 'auto', md: '0' }}
                    mb={3}
                    bg="primary"
                    color="black"
                  >
                    Learn More
                  </Button>
                </Box>
                <Divider
                  borderColor="whiteAlpha.500"
                  my={2}
                  display={{ base: 'flex', md: 'none' }}
                />

                {/* Image Placeholder */}
                <Flex
                  w={{ base: 'full', md: '50%' }}
                  justify="center"
                  align="center"
                  mt={{ base: 2, md: 0 }}
                >
                  <Box
                    w="full"
                    maxW={{ base: '180px', md: '240px', lg: '280px' }}
                    h={{ base: '150px', md: '200px', lg: '240px' }}
                    bg="whiteAlpha.300"
                    borderRadius="xl"
                    border="2px dashed"
                    borderColor="whiteAlpha.500"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text
                      fontSize="sm"
                      fontWeight="bold"
                      color="whiteAlpha.700"
                    >
                      Product Image
                    </Text>
                  </Box>
                </Flex>
              </Flex>
            </Flex>
          </SwiperSlide>
        ))}

        {/* Custom Navigation Buttons */}
        <Box
          className="swiper-button-prev"
          position="absolute"
          left="2em"
          top="50%"
          zIndex={10}
          transform="translateY(-50%)"
          display={{ base: 'none', md: 'flex' }}
          w="45px"
          h="45px"
          px="5px"
          borderRadius="full"
          bg="whiteAlpha.600"
          color="blackAlpha.800"
          cursor="pointer"
          boxShadow="md"
          _hover={{ bg: 'whiteAlpha.500' }}
        >
          <FiChevronLeft className="absolute left-0" />
        </Box>
        <Box
          className="swiper-button-next"
          position="absolute"
          right="2em"
          top="50%"
          zIndex={10}
          transform="translateY(-50%)"
          display={{ base: 'none', md: 'flex' }}
          w="45px"
          h="45px"
          px="5px"
          borderRadius="full"
          bg="whiteAlpha.600"
          cursor="pointer"
          color="blackAlpha.800"
          boxShadow="md"
          _hover={{ bg: 'whiteAlpha.500' }}
        >
          <FiChevronRight className="absolute" />
        </Box>
      </Swiper>
    </Box>
  );
};

export default HeroSection;
