-- Vendor Policy Updates Migration - 20251002232000
-- This migration updates RLS policies for vendor system to remove admin-only access
-- and add proper vendor-specific document and storage policies

-- 1. Drop all existing vendor RLS policies to allow creation without login
DROP POLICY IF EXISTS "Vendors can view their own data" ON public.vendor;
DROP POLICY IF EXISTS "Vendors can update their own data" ON public.vendor;
DROP POLICY IF EXISTS "Vendors can insert their own data" ON public.vendor;
DROP POLICY IF EXISTS "Public can view approved vendors" ON public.vendor;
DROP POLICY IF EXISTS "Vendors are visible to everyone" ON public.vendor;

-- 2. Drop existing admin policies that are being removed
DROP POLICY IF EXISTS "Ad<PERSON> can view all vendor documents" ON public.vendor_documents;
DROP POLICY IF EXISTS "Admins can update vendor documents" ON public.vendor_documents;
DROP POLICY IF EXISTS "Ad<PERSON> can view all vendor versions" ON public.vendor_versions;
DROP POLICY IF EXISTS "Admins can update vendor versions" ON public.vendor_versions;

-- 3 Drop existing vendor documents policies that are being removed
DROP POLICY IF EXISTS "Vendors can view their own documents" ON public.vendor_documents;

-- 3. Add vendor-specific document insertion policy
CREATE POLICY "Vendors can insert their own documents" ON public.vendor_documents
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.vendor 
            WHERE id = vendor_id
        )
    );

-- 4. Drop existing storage policies first
DROP POLICY IF EXISTS "Vendors can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Vendors can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Vendors can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Vendors can delete their own documents" ON storage.objects;

-- 5. Create vendor storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('vendor', 'vendor', true)
ON CONFLICT (id) DO NOTHING;

-- 6. Disable RLS on vendor table to allow creation without login
ALTER TABLE public.vendor DISABLE ROW LEVEL SECURITY;
