'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';

// Define state types
export type LoginState = {
  error: string | null;
};

export type SignupState = {
  error: string | null;
  success: boolean;
  message: string | null;
};

export async function login(
  _prevState: LoginState | undefined,
  formData: FormData
): Promise<LoginState> {
  const supabase = await createClient();
  const { error } = await supabase.auth.signInWithPassword({
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  });

  if (error) {
    return {
      error: 'Failed to login. Please check email and password and try again.',
    };
  }

  revalidatePath('/', 'layout');
  // Redirect directly from server action
  redirect('/');
  // This return will never be reached but satisfies TypeScript
  return { error: null };
}

export async function signupOrLogin(
  _prevState: LoginState | undefined,
  formData: FormData
): Promise<LoginState> {
  const supabase = await createClient();

  const { error } = await supabase.auth.signInWithOtp({
    email: formData.get('email') as string,
    options: {
      // Prevent creating a new user if the email doesn't exist
      shouldCreateUser: true,
    },
  });

  if (error) {
    return { error: 'Failed to send OTP' };
  }

  revalidatePath('/', 'layout');
  return { error: null };
}

export async function loginWithOTP(
  _prevState: LoginState | undefined,
  formData: FormData
): Promise<LoginState> {
  const supabase = await createClient();

  const { error } = await supabase.auth.verifyOtp({
    email: formData.get('email') as string,
    token: formData.get('otp') as string,
    type: 'email',
  });

  if (error) {
    return { error: 'Failed to verify OTP' };
  }

  revalidatePath('/', 'layout');
  return { error: null };
}

export async function signup(
  _prevState: SignupState | undefined,
  formData: FormData
): Promise<SignupState> {
  const supabase = await createClient();

  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  if (password !== confirmPassword) {
    return {
      error: 'Passwords do not match',
      success: false,
      message: null,
    };
  }

  if (password.length < 8) {
    return {
      error: 'Password must be at least 8 characters long',
      success: false,
      message: null,
    };
  }

  const { error } = await supabase.auth.signUp({
    email,
    password,
  });

  if (error) {
    return {
      error: error.message,
      success: false,
      message: null,
    };
  }

  return {
    error: null,
    success: true,
    message: 'Check your email for the confirmation link!',
  };
}
