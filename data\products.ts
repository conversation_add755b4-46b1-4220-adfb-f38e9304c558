// Unified categories with nested subcategories
type Subcategory = {
  id: string;
  name: string;
};

type Category = {
  id: string;
  name: string;
  subcategories: Subcategory[];
};

const categories = [
  {
    id: 'electronics',
    name: 'Electronics & Technology',
    subcategories: [
      { id: 'computers', name: 'Computers & Accessories' },
      { id: 'mobile', name: 'Mobile & Tablets' },
      { id: 'tv', name: 'TV & Home Theater' },
      { id: 'audio', name: 'Audio & Headphones' },
      { id: 'cameras', name: 'Cameras & Photography' },
    ],
  },
  {
    id: 'fashion',
    name: 'Fashion & Accessories',
    subcategories: [
      { id: 'mens', name: "Men's Clothing" },
      { id: 'womens', name: "Women's Clothing" },
      { id: 'shoes', name: 'Shoes & Footwear' },
      { id: 'jewelry', name: 'Jewelry & Watches' },
      { id: 'bags', name: 'Bags & Luggage' },
    ],
  },
  {
    id: 'home',
    name: 'Home & Kitchen',
    subcategories: [
      { id: 'furniture', name: 'Furniture' },
      { id: 'appliances', name: 'Kitchen Appliances' },
      { id: 'decor', name: 'Home Decor' },
      { id: 'bedbath', name: 'Bed & Bath' },
      { id: 'lighting', name: 'Lighting' },
    ],
  },
  {
    id: 'beauty',
    name: 'Beauty & Personal Care',
    subcategories: [
      { id: 'skincare', name: 'Skincare' },
      { id: 'makeup', name: 'Makeup' },
      { id: 'hair', name: 'Hair Care' },
      { id: 'fragrances', name: 'Fragrances' },
      { id: 'hygiene', name: 'Personal Hygiene' },
    ],
  },
  {
    id: 'sports',
    name: 'Sports & Outdoors',
    subcategories: [
      { id: 'fitness', name: 'Exercise & Fitness' },
      { id: 'outdoor', name: 'Outdoor Recreation' },
      { id: 'teamsports', name: 'Team Sports' },
      { id: 'athletic', name: 'Athletic Clothing' },
      { id: 'equipment', name: 'Sports Equipment' },
    ],
  },
  {
    id: 'automotive',
    name: 'Automotive & Industrial',
    subcategories: [
      { id: 'carparts', name: 'Car Parts' },
      { id: 'tools', name: 'Tools & Equipment' },
      { id: 'motorcycle', name: 'Motorcycle Accessories' },
      { id: 'industrial', name: 'Industrial Supplies' },
    ],
  },
  {
    id: 'books',
    name: 'Books & Media',
    subcategories: [
      { id: 'fiction', name: 'Fiction' },
      { id: 'nonfiction', name: 'Non-Fiction' },
      { id: 'educational', name: 'Educational' },
      { id: 'magazines', name: 'Magazines' },
    ],
  },
  {
    id: 'toys',
    name: 'Toys & Games',
    subcategories: [
      { id: 'boardgames', name: 'Board Games' },
      { id: 'videogames', name: 'Video Games' },
      { id: 'outdoortoys', name: 'Outdoor Toys' },
      { id: 'educationaltoys', name: 'Educational Toys' },
    ],
  },
  {
    id: 'health',
    name: 'Health & Wellness',
    subcategories: [
      { id: 'vitamins', name: 'Vitamins & Supplements' },
      { id: 'medical', name: 'Medical Equipment' },
      { id: 'trackers', name: 'Fitness Trackers' },
      { id: 'wellness', name: 'Wellness Products' },
    ],
  },
  {
    id: 'grocery',
    name: 'Grocery & Gourmet',
    subcategories: [
      { id: 'organic', name: 'Organic Foods' },
      { id: 'international', name: 'International Cuisine' },
      { id: 'beverages', name: 'Beverages' },
      { id: 'snacks', name: 'Snacks' },
    ],
  },
];

const productInfo = {
  highlights: [
    'Top notes are Granny Smith Apple, Bitter Orange and Mandarin Orange',
    'Middle notes are Tea, Sea Water, Geranium, Nutmeg and Cardamom',
    'Base notes are White Musk, Virginia Cedar, Tonka Bean, Vetiver, Sandalwood and Oak Moss',
    'Fragrances in an elegant, classic style always enjoy great success',
    'Take Jaguar Classic Black, for example, a stupendous scent composition',
  ],
  overview: {
    aboutProduct:
      'Classic Black by Jaguar is a Oriental Fougere fragrance for men. Classic Black was launched in 2009. The nose behind this fragrance is Dominique Preyssas. Top notes are Granny Smith apple, Bitter Orange and Mandarin Orange; middle notes are Tea, Sea Water, Geranium, Nutmeg and Cardamom; base notes are White Musk, Virginia Cedar, Tonka Bean, Vetiver, Sandalwood and oak moss.',
    aboutBrand:
      'Jaguar is popular amongst many well-known celebrities. The first Jaguar perfume was created in 1986. Like the car, it is of high quality, original, and full of power and luxury. Jaguar fragrance is not just produced for men but women too who like to treat themselves and allow themselves to be dominated by a unique sensuality.',
    ingredients:
      'Alcohol, Parfum (Fragrance), Aqua (Water), Limonene, Ethylhexyl Methoxycinnamate, Diethylamino, Hydroxybenzoyl Hexylbenzoate, Citral, Cinnamal, Linlool, Bht.',
    howToUse: 'Spray on pulse points: behind the ears and on your wrists.',
  },
  specifications: [
    { key: 'Fragrance Size', value: '100 - 119 ml' },
    { key: 'Scents/Notes', value: 'Woody' },
    { key: 'Colour Name', value: 'clear' },
    {
      key: 'Base Note',
      value:
        'White Musk, Virginia Cedar, Tonka Bean, Vetiver, Sandalwood, Oak Moss',
    },
    {
      key: 'Heart/Middle Note',
      value: 'Tea, Sea Water, Geranium, Nutmeg, Cardamom',
    },
    {
      key: 'Top Note',
      value: 'Granny Smith Apple, Bitter Orange, Mandarin Orange',
    },
    { key: 'Size', value: '100 ml' },
    { key: 'Department', value: 'Men' },
    { key: 'Dispenser Type', value: 'Splash' },
    { key: 'Product Subtype', value: 'Eau de Toilette' },
    { key: 'Model Number', value: '10001096' },
    { key: 'Model Name', value: 'Perfume' },
  ],
};

export { categories, productInfo };
export type { Category, Subcategory };
