export interface UserProfile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  phone: string | null;
  address: {
    street?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    country?: string;
  } | null;
  preferences: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export type ProfileFormData = Pick<
  UserProfile,
  'first_name' | 'last_name' | 'phone' | 'address'
>;
