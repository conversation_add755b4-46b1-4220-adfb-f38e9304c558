'use client';
import { VStack, Heading, Text, Icon, Button, Box } from '@chakra-ui/react';
import { FiXCircle } from 'react-icons/fi';

type FailureDisplayProps = {
  message: string;
  onTryAgain: () => void;
};

export const FailureDisplay = ({
  message,
  onTryAgain,
}: FailureDisplayProps) => {
  return (
    <VStack
      bg="white"
      p={{ base: 6, md: 10 }}
      borderRadius="xl"
      spacing={6}
      textAlign="center"
      maxW="lg"
      mx="auto"
    >
      <Icon as={FiXCircle} boxSize={{ base: 12, md: 16 }} color="red.500" />
      <VStack spacing={2}>
        <Heading as="h2" size="lg">
          Payment Failed
        </Heading>
        <Text color="gray.600">
          Unfortunately, we were unable to process your payment.
        </Text>
        <Text fontSize="sm" color="gray.500" pt={2}>
          Reason: {message || 'An unknown error occurred.'}
        </Text>
      </VStack>
      <Box pt={4}>
        <Button onClick={onTryAgain} colorScheme="blue">
          Try Again
        </Button>
      </Box>
    </VStack>
  );
};
