'use server';

import { createClient } from '@/utils/supabase/server';
import { CartItem } from '@/providers/CartContext';

/**
 * Saves or updates a cart. This is called when the user proceeds from the review step.
 */
export async function saveCartAction(
  item: CartItem,
  quantity: number,
  cartId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    console.log('saveCartAction called with:', {
      item,
      quantity,
      cartId,
      user,
    });
    if (!item || quantity <= 0 || !cartId) {
      throw new Error('Invalid cart data provided to server action.');
    }

    // const { quantity: _, ...itemData } = item;

    // const cartData = {
    //   id: cartId,
    //   user_id: user?.id,
    //   item_details: itemData,
    //   quantity: quantity,
    //   updated_at: new Date().toISOString(),
    // };

    // const { error } = await supabase
    //   .from('cart')
    //   .upsert({
    //     user_id: user?.id!,
    //     circle_id: item.id,
    //     quantity: quantity,
    //   });

    // if (error) {
    //   console.error('Supabase upsert error in saveCartAction:', error);
    //   throw new Error(`Database error: ${error.message}`);
    // }

    return { success: true };
  } catch (error) {
    console.error('saveCartAction Error:', error);
    return { success: false, error: `${error}` };
  }
}
