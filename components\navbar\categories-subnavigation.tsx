'use client';
import React, { useState, useRef, useEffect } from 'react';
import { Box, Flex, IconButton } from '@chakra-ui/react';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { Link } from '@chakra-ui/next-js';
import NextLink from 'next/link';
import { usePathname } from 'next/navigation';
import { Category } from '@/utils/supabase/queries';

const CategoriesSubnav = ({ categories }: { categories: Category[] }) => {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const categoriesRef = useRef<HTMLDivElement>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);
  const pathname = usePathname();

  // Handle horizontal scrolling
  const handleScroll = (direction: 'left' | 'right') => {
    if (categoriesRef.current) {
      const scrollAmount = 300;
      if (direction === 'right') {
        categoriesRef.current.scrollBy({
          left: scrollAmount,
          behavior: 'smooth',
        });
      } else {
        categoriesRef.current.scrollBy({
          left: -scrollAmount,
          behavior: 'smooth',
        });
      }
    }
  };

  // Check scroll position to show/hide arrows
  const checkScroll = () => {
    if (categoriesRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = categoriesRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  // Use ResizeObserver to detect container size changes
  useEffect(() => {
    if (!categoriesRef.current) return;

    // Initialize ResizeObserver
    resizeObserverRef.current = new ResizeObserver(checkScroll);
    resizeObserverRef.current.observe(categoriesRef.current);

    // Check initial scroll position
    checkScroll();

    // Add scroll event listener
    categoriesRef.current.addEventListener('scroll', checkScroll);

    return () => {
      if (resizeObserverRef.current && categoriesRef.current) {
        resizeObserverRef.current.unobserve(categoriesRef.current);
      }
      if (categoriesRef.current) {
        categoriesRef.current.removeEventListener('scroll', checkScroll);
      }
    };
  }, []);
  if (pathname.startsWith('/checkout')) {
    return null;
  }
  return (
    <Box bg="white" borderBottom="1px" borderColor="blackAlpha.400">
      <Box
        position="relative"
        display={{ base: 'none', md: 'block' }}
        bg="white"
        py={2}
        mx="auto"
        maxW="container.xl"
      >
        {/* Left scroll arrow */}
        <IconButton
          aria-label="Scroll left"
          icon={<FiChevronLeft />}
          variant="ghost"
          position="absolute"
          left="0"
          top="50%"
          transform="translateY(-50%)"
          zIndex={2}
          bg="white"
          boxShadow="md"
          color="black"
          opacity={showLeftArrow ? 1 : 0}
          pointerEvents={showLeftArrow ? 'auto' : 'none'}
          _hover={{ bg: 'gray.100' }}
          onClick={() => handleScroll('left')}
          transition="opacity 0.2s"
          borderRadius="full"
          border="1px"
          borderColor="blackAlpha.700"
        />

        {/* Categories container */}
        <Flex
          ref={categoriesRef}
          wrap="nowrap"
          overflowX="auto"
          css={{
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          {categories.map(category => {
            const isActive = pathname.startsWith(`/category/${category.slug}`);
            return (
              <Link
                key={category.id}
                as={NextLink}
                href={`/category/${category.slug}`}
                minW="max-content"
                py={2}
                mr={4}
                fontWeight={isActive ? 'bold' : 'normal'}
                position="relative"
                _hover={{
                  textDecoration: 'none',
                  color: 'blackAlpha.800',
                }}
                color={isActive ? 'black' : 'blackAlpha.800'}
              >
                {category.name}
              </Link>
            );
          })}
        </Flex>

        {/* Right scroll arrow */}
        <IconButton
          aria-label="Scroll right"
          icon={<FiChevronRight />}
          variant="ghost"
          position="absolute"
          right="0"
          top="50%"
          transform="translateY(-50%)"
          zIndex={2}
          bg="white"
          boxShadow="md"
          color="black"
          opacity={showRightArrow ? 1 : 0}
          pointerEvents={showRightArrow ? 'auto' : 'none'}
          _hover={{ bg: 'gray.100' }}
          onClick={() => handleScroll('right')}
          transition="opacity 0.2s"
          borderRadius="full"
          border="1px"
          borderColor="blackAlpha.700"
        />
      </Box>
    </Box>
  );
};

export default CategoriesSubnav;
