'use client';

import { useActionState, useRef, useEffect } from 'react';
import { vendorLogin } from '@/app/actions/vendor-auth';
import NextLink from 'next/link';
import { useToast } from '@chakra-ui/react';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Link as ChakraLink,
  Spinner,
  HStack,
} from '@chakra-ui/react';

export default function VendorLoginForm() {
  const formRef = useRef<HTMLFormElement>(null);
  const toast = useToast();

  // Use the new useActionState hook
  const [formState, formAction, isPending] = useActionState(vendorLogin, {
    error: null,
  });

  useEffect(() => {
    if (formState.error) {
      // Reset the form after showing error
      formRef.current?.reset();
      toast({
        title: 'Login Error',
        description: formState.error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }, [formState.error, toast]);

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="md"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Vendor Login
          </Heading>

          <Text textAlign="center" color="gray.600" fontSize="sm">
            Login to your vendor account
          </Text>

          <form ref={formRef} action={formAction}>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel htmlFor="email" color="gray.600">
                  Business Email
                </FormLabel>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  focusBorderColor="primary"
                  placeholder="<EMAIL>"
                  disabled={isPending}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel htmlFor="password" color="gray.600">
                  Password
                </FormLabel>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  focusBorderColor="primary"
                  disabled={isPending}
                />
                <Flex justify="flex-end" mt={2}>
                  <ChakraLink
                    as={NextLink}
                    href="/vendor/auth/forgot-password"
                    fontSize="sm"
                    color="blue.600"
                    fontWeight="medium"
                    _hover={{ color: 'blue.500' }}
                  >
                    Forgot password?
                  </ChakraLink>
                </Flex>
              </FormControl>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Sign In
              </Button>
            </VStack>
          </form>

          <HStack justify="center" spacing={4}>
            <Text textAlign="center" fontSize="sm" color="gray.600">
              Don&apos;t have a vendor account?
            </Text>
            <ChakraLink
              as={NextLink}
              href="/vendor/auth/signup"
              color="blue.600"
              fontWeight="medium"
              _hover={{ color: 'blue.500' }}
            >
              Sign Up
            </ChakraLink>
          </HStack>
        </VStack>
      </Box>
    </Flex>
  );
}
