import { Box, Heading, VStack, Flex, Text } from '@chakra-ui/react';

interface OrderSummaryProps {
  summary: {
    subtotal: number;
    shippingFee: number | 'Free';
    total: number;
  };
}
const OrderSummary = ({ summary }: OrderSummaryProps) => {
  const shippingDisplay =
    summary.shippingFee === 'Free'
      ? 'Free'
      : `SAR ${summary.shippingFee.toFixed(2)}`;

  return (
    <Box bg="white" p={6} borderRadius="xl">
      <VStack align="stretch" spacing={4}>
        <Heading as="h2" size="md" fontWeight="bold">
          Order Summary
        </Heading>

        <VStack align="stretch" spacing={2} py={2}>
          <Flex justify="space-between">
            <Text color="gray.600">Subtotal</Text>
            <Text fontWeight="medium">SAR {summary.subtotal.toFixed(2)}</Text>
          </Flex>
          <Flex justify="space-between">
            <Text color="gray.600">Shipping Fee</Text>
            <Text fontWeight="medium">{shippingDisplay}</Text>
          </Flex>
        </VStack>

        <Flex
          justify="space-between"
          align="center"
          borderTop="1px solid"
          borderColor="gray.200"
          pt={4}
        >
          <Text fontSize="lg" fontWeight="bold">
            Total
          </Text>
          <Text fontSize="2xl" fontWeight="bold">
            SAR {summary.total.toFixed(2)}
          </Text>
        </Flex>
      </VStack>
    </Box>
  );
};

export { OrderSummary };
