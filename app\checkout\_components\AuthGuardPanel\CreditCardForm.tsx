'use client';
import {
  Box,
  FormControl,
  Grid,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
} from '@chakra-ui/react';
import { FiLock } from 'react-icons/fi';

export const CreditCardForm = () => {
  return (
    <VStack spacing={4} align="stretch">
      {/* 
        This is the target for the Moyasar Drop-in UI.
        For the UI build, we are creating a high-fidelity placeholder.
        In the future, the real Moyasar element will replace the Inputs below.
      */}
      <Box id="moyasar-form-container">
        <FormControl>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <FiLock color="gray.400" />
            </InputLeftElement>
            <Input placeholder="Card Number" bg="gray.50" />
          </InputGroup>
        </FormControl>
        <Grid templateColumns="repeat(2, 1fr)" gap={4} mt={4}>
          <FormControl>
            <Input placeholder="MM / YY" bg="gray.50" />
          </FormControl>
          <FormControl>
            <Input placeholder="CVC" bg="gray.50" />
          </FormControl>
        </Grid>
      </Box>
    </VStack>
  );
};
