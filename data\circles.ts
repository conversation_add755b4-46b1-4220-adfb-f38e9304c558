const circlesData = [
  {
    id: 1,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '2h:14m',
  },
  {
    id: 2,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1832,
    totalJoiners: 1000,
    isFull: true,
    marketPrice: 1989,
    nextPrice: 1562,
    timer: '00h:00m',
  },
  {
    id: 3,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1280,
    totalJoiners: 350,
    nextPrice: 1456,
    marketPrice: 1989,
    timer: '2h:00m',
  },
  {
    id: 4,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1456,
    totalJoiners: 850,
    nextPrice: 1832,
    marketPrice: 1989,
    timer: '00h:45m',
  },
  {
    id: 5,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '2h:14m',
  },
  {
    id: 6,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '2h:14m',
  },
];

// Data tailored for the "Ends Soon" section design
const flashCirclesData = [
  {
    id: 1,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '00h:14m',
  },
  {
    id: 2,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1456,
    totalJoiners: 850,
    nextPrice: 1832,
    marketPrice: 1989,
    timer: '00h:45m',
  },
  {
    id: 3,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1280,
    totalJoiners: 350,
    nextPrice: 1456,
    marketPrice: 1989,
    timer: '00h:30m',
  },
  {
    id: 4,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1456,
    totalJoiners: 800, // Slightly different value for visual variety
    nextPrice: 1832,
    marketPrice: 1989,
    timer: '00h:12m',
  },
  {
    id: 5,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '00h:14m',
  },
];

const suggestedCirclesData = [
  {
    id: 1,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1456,
    totalJoiners: 850,
    nextPrice: 1832,
    marketPrice: 1989,
    timer: '00h:12m',
  },
  {
    id: 2,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1280,
    totalJoiners: 350,
    nextPrice: 1456,
    marketPrice: 1989,
    timer: '2h:00m',
  },
  {
    id: 3,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1008,
    totalJoiners: 200,
    nextPrice: 1280,
    marketPrice: 1989,
    timer: '2h:14m',
  },
  {
    id: 4,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1456,
    totalJoiners: 850,
    nextPrice: 1832,
    marketPrice: 1989,
    timer: '00h:12m',
  },
  {
    id: 5,
    productName: 'PlayStation VR 2',
    imageUrl: '/product.png',
    price: 1280,
    totalJoiners: 350,
    nextPrice: 1456,
    marketPrice: 1989,
    timer: '2h:00m',
  },
];

export { suggestedCirclesData, circlesData, flashCirclesData };
