import {
  Flex,
  Text,
  Image,
  HStack,
  VStack,
  IconButton,
} from '@chakra-ui/react';
import type { CartItem as CartItemType } from '@/providers/CartContext';
import { useCart } from '@/providers/CartContext';
import { FiPlus, FiMinus, FiTrash2 } from 'react-icons/fi';

interface CartItemProps {
  item: CartItemType;
}

const CartItem = ({ item }: CartItemProps) => {
  const { updateItemQuantity, removeItem } = useCart();

  return (
    <Flex
      align="center"
      justify="space-between"
      w="full"
      flexDir={{ base: 'column', md: 'row' }}
    >
      <HStack
        align="center"
        spacing={4}
        flexDir={{ base: 'column', md: 'row' }}
      >
        <Image
          src={item.product?.product_image || ''}
          alt={`product image`}
          boxSize="150px"
          objectFit="contain"
          borderRadius="md"
          border="1px solid"
          borderColor="gray.200"
        />
        <VStack align={{ base: 'center', md: 'start' }} spacing={1}>
          <Text
            fontWeight="medium"
            color="gray.800"
            noOfLines={2}
            align={{ base: 'center', md: 'start' }}
          >
            {item.product?.name || item.description}
          </Text>
          <Text
            fontSize="sm"
            color="gray.500"
            align={{ base: 'center', md: 'start' }}
          >
            ID: {item.product_id}
          </Text>
        </VStack>
      </HStack>

      <VStack align={{ base: 'center', md: 'start' }} spacing={2}>
        <Text fontSize="lg" fontWeight="bold" color="gray.900">
          SAR {item.sale_price.toFixed(2)}
        </Text>
        <HStack>
          <IconButton
            size="sm"
            aria-label="Decrease quantity"
            icon={<FiMinus />}
            onClick={() => updateItemQuantity(item.quantity - 1)}
          />
          <Text w="40px" textAlign="center" fontWeight="bold">
            {item.quantity}
          </Text>
          <IconButton
            size="sm"
            aria-label="Increase quantity"
            icon={<FiPlus />}
            onClick={() => updateItemQuantity(item.quantity + 1)}
          />
          <IconButton
            size="sm"
            variant="ghost"
            colorScheme="red"
            aria-label="Remove item"
            icon={<FiTrash2 />}
            onClick={() => removeItem()}
          />
        </HStack>
      </VStack>
    </Flex>
  );
};

export { CartItem };
