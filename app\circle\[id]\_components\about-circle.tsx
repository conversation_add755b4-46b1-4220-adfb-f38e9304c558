'use client';

import React from 'react';
import {
  Box,
  <PERSON>ton,
  <PERSON>lapse,
  Divider,
  Grid,
  GridItem,
  Heading,
  Text,
  UnorderedList,
  ListItem,
  useDisclosure,
  VStack,
  Flex,
} from '@chakra-ui/react';

interface Overview {
  aboutProduct?: string;
  aboutBrand?: string;
  ingredients?: string;
  howToUse?: string;
}

interface Specification {
  key: string;
  value: string;
}

interface AboutData {
  highlights?: string[];
  overview?: Overview;
  specifications?: Specification[];
}

interface AboutCircleProps {
  about?: AboutData | null;
}

const AboutCircle: React.FC<AboutCircleProps> = ({ about }) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });

  // We destructure with safe fallbacks. If 'about' is null or undefined,
  // these will default to empty arrays/objects.
  const highlights = about?.highlights ?? [];
  const overview = about?.overview ?? {};
  const specifications = about?.specifications ?? [];

  // Check if there is any meaningful data to display in each section.
  const hasHighlights = highlights.some(item => item && item.trim() !== '');
  const hasOverview = Object.values(overview).some(
    value => value && value.trim() !== ''
  );
  const hasSpecifications = specifications.some(
    spec => spec.key && spec.key.trim() !== ''
  );

  // --- MASTER GUARD CLAUSE ---
  // If there is no data at all, render nothing. This prevents an empty component shell.
  if (!hasHighlights && !hasOverview && !hasSpecifications) {
    return null;
  }

  return (
    <Box
      bg="white"
      maxW="container.xl"
      mx="auto"
      p={{ base: 4, md: 8 }}
      borderRadius="xl"
    >
      <VStack align="stretch" spacing={5}>
        <Heading as="h2" size="lg" fontWeight="bold">
          About this product
        </Heading>
        <Divider />

        <Collapse in={isOpen} animateOpacity>
          <Grid
            templateColumns={{ base: 'repeat(1, 1fr)', lg: 'repeat(3, 1fr)' }}
            gap={{ base: 8, lg: 16 }}
            pt={1}
          >
            {/* --- LEFT COLUMN: Highlights & Overview --- */}
            <GridItem colSpan={{ base: 1, lg: 2 }}>
              <VStack align="stretch" spacing={8}>
                {/* Only render this section if there are valid highlights */}
                {hasHighlights && (
                  <VStack align="stretch" spacing={4}>
                    <Heading as="h3" size="md" fontWeight="semibold">
                      Highlights
                    </Heading>
                    <UnorderedList spacing={2} pl={4}>
                      {highlights.map((item, index) =>
                        item ? ( // Render list item only if item is not empty
                          <ListItem key={index} color="gray.700">
                            {item}
                          </ListItem>
                        ) : null
                      )}
                    </UnorderedList>
                  </VStack>
                )}

                {/* Only render this section if there is valid overview data */}
                {hasOverview && (
                  <VStack align="stretch" spacing={4}>
                    <Heading as="h3" size="md" fontWeight="semibold">
                      Overview
                    </Heading>
                    <VStack align="stretch" spacing={4} color="gray.700">
                      {overview.aboutProduct && (
                        <Box>
                          <Text fontWeight="semibold" mb={1}>
                            About the product:
                          </Text>
                          <Text>{overview.aboutProduct}</Text>
                        </Box>
                      )}
                      {overview.aboutBrand && (
                        <Box>
                          <Text fontWeight="semibold" mb={1}>
                            About the brand:
                          </Text>
                          <Text>{overview.aboutBrand}</Text>
                        </Box>
                      )}
                      {overview.ingredients && (
                        <Box>
                          <Text fontWeight="semibold" mb={1}>
                            Ingredients:
                          </Text>
                          <Text>{overview.ingredients}</Text>
                        </Box>
                      )}
                      {overview.howToUse && (
                        <Box>
                          <Text fontWeight="semibold" mb={1}>
                            How to use:
                          </Text>
                          <Text>{overview.howToUse}</Text>
                        </Box>
                      )}
                    </VStack>
                  </VStack>
                )}
              </VStack>
            </GridItem>

            {/* --- RIGHT COLUMN: Specifications --- */}
            {hasSpecifications && (
              <GridItem colSpan={{ base: 1, lg: 1 }}>
                <VStack align="stretch" spacing={4}>
                  <Heading as="h3" size="md" fontWeight="semibold">
                    Specifications
                  </Heading>
                  <VStack align="stretch" spacing={3}>
                    {specifications.map((spec, index) =>
                      spec.key ? ( // Render spec only if it has a key
                        <React.Fragment key={spec.key}>
                          <Flex justify="space-between" align="center" py={2}>
                            <Text fontSize="sm" color="gray.600">
                              {spec.key}
                            </Text>
                            <Text
                              fontSize="sm"
                              color="gray.800"
                              fontWeight="medium"
                              textAlign="right"
                            >
                              {spec.value}
                            </Text>
                          </Flex>
                          {index < specifications.length - 1 && <Divider />}
                        </React.Fragment>
                      ) : null
                    )}
                  </VStack>
                </VStack>
              </GridItem>
            )}
          </Grid>
        </Collapse>

        {/* Toggle Button - Fixed */}
        <Flex justify="center" pt={4}>
          <Button
            onClick={onToggle}
            variant="outline"
            colorScheme="gray"
            fontWeight="semibold"
            px={8}
            borderColor="gray.400"
          >
            {isOpen ? 'Show Less' : 'Show More'}
          </Button>
        </Flex>
      </VStack>
    </Box>
  );
};

export default AboutCircle;
