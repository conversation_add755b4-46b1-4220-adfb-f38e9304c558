import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorLoginForm from './_components/VendorLoginForm';

export default async function VendorLoginPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    // Check if user's email is verified
    if (!user.email_confirmed_at) {
      // User is logged in but email not verified, let them stay on login page
      // The login form will handle the email verification error
      return <VendorLoginForm />;
    }

    // Check vendor status and redirect accordingly
    const { data: vendor } = await supabase
      .from('vendor')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (vendor) {
      // Check if company info is complete - all required fields
      if (vendor.headline && vendor.description && vendor.date_of_incorporation) {
        redirect('/vendor/dashboard');
      } else {
        redirect('/vendor/auth/company-info');
      }
    } else {
      redirect('/vendor/auth/signup');
    }
  }

  return <VendorLoginForm />;
}
