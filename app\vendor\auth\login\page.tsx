import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorLoginForm from './_components/VendorLoginForm';

export default async function VendorLoginPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    // Check vendor status and redirect accordingly
    const { data: vendor } = await supabase
      .from('vendor')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (vendor) {
      if (vendor.headline && vendor.description) {
        redirect('/vendor/dashboard');
      } else {
        redirect('/vendor/auth/company-info');
      }
    } else {
      redirect('/vendor/auth/signup');
    }
  }

  return <VendorLoginForm />;
}
