'use server';
import Breadcrumbs from '@/components/navbar/bread-crumbs';
import { notFound } from 'next/navigation';
import React from 'react';
import {
  CircleDetails,
  AboutCircle,
  ReviewsSection,
  RelatedCircles,
} from './_components';
import { getRelatedCircles, getCircleById } from '@/utils/supabase/queries';

interface CirclePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function CirclePage({ params }: CirclePageProps) {
  /**
   *  Ideally later breadcrumbs items
   *  Are the circle (product categories (main-> sub -> sub (if any)))
   *  For now dummy data
   * */

  const { id } = await params;
  const circle = await getCircleById(id);
  const related_circles = await getRelatedCircles(id);
  if (!circle) {
    notFound();
  }
  console.log(related_circles);
  const categories = [
    circle?.product?.main_category,
    circle?.product?.category_1,
    circle?.product?.category_2,
  ]
    .filter(Boolean)
    .map(cat => cat?.name ?? ''); // Filter out null or undefined categories
  return (
    <main className="container mx-auto px-4 py-8 relative">
      <Breadcrumbs items={categories} />
      <CircleDetails circle={circle} />
      <AboutCircle
        about={{
          highlights: [],
          overview: {},
          specifications: [],
        }}
      />
      <ReviewsSection />

      <RelatedCircles relatedCircles={related_circles} />
    </main>
  );
}

export async function generateMetadata({ params }: CirclePageProps) {
  const { id } = await params;
  const circle = await getCircleById(id);

  if (!circle) {
    return {
      title: 'Product Not Found',
    };
  }

  return {
    title: circle.product?.name,
    description: circle.description,
  };
}
