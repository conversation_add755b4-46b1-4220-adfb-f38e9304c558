import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { ProfileForm } from './ProfileForm';

export default async function ProfilePage() {
  const supabase = await createClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    redirect('/auth/login');
  }

  const { data: profile, error } = await supabase
    .from('user_profile')
    .select('*')
    .eq('id', session.user.id)
    .single();

  if (error) {
    console.error('Error loading profile:', error);
    throw new Error('Failed to load profile');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <ProfileForm profile={profile} />
      </div>
    </div>
  );
}
