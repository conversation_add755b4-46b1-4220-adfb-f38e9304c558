'use client';
import { Box, Button, HStack, Image, Text, VStack } from '@chakra-ui/react';
import type { CartItem } from '@/providers/CartContext';

// --- J<PERSON>'s Fix: Add an onRemove prop ---
type OrderItemCardProps = {
  item: CartItem;
  onRemove: () => void;
};

export const OrderItemCard = ({ item, onRemove }: OrderItemCardProps) => {
  return (
    <Box borderWidth="1px" borderColor="gray.200" borderRadius="xl" p={4}>
      <HStack spacing={4} align="start">
        <Image
          src={item.product?.product_image || '/placeholder.png'}
          alt={item.description || 'Product Image'}
          boxSize="100px"
          objectFit="cover"
          borderRadius="md"
        />
        <VStack align="start" spacing={1} flex="1">
          <Text fontWeight="medium" noOfLines={2}>
            {item.description}
          </Text>
          <Text fontSize="sm" color="gray.500">
            Delivered by Sun, May 10
          </Text>
          {/* replace this with a link to /profile or /orders where we show user's orders */}
          <Button
            variant="link"
            colorScheme="red"
            size="sm"
            onClick={onRemove}
            fontWeight="normal"
          >
            Remove
          </Button>
        </VStack>
        <VStack align="end" spacing={1}>
          <Text fontWeight="bold" fontSize="lg">
            SAR {item.sale_price.toFixed(2)}
          </Text>
          <Text fontSize="sm" color="gray.500">
            QTY: {item.quantity}
          </Text>
        </VStack>
      </HStack>
    </Box>
  );
};
