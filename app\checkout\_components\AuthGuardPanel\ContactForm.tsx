'use client';
import { User } from '@supabase/supabase-js';
import {
  VStack,
  FormControl,
  FormLabel,
  Input,
  Button,
  Heading,
  HStack,
  Text,
  Link,
  Icon,
  FormErrorMessage,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';
import { FiCheckCircle } from 'react-icons/fi';
import { useCart } from '@/providers/CartContext';
import { loginWithOTP, signupOrLogin } from '@/app/actions/auth';

type ContactFormProps = {
  user: User | null;
  onVerificationSuccess: (isVerified: boolean) => void;
};

type UiState = 'idle' | 'verifying' | 'verified';

export const ContactForm = ({
  user,
  onVerificationSuccess,
}: ContactFormProps) => {
  const [uiState, setUiState] = useState<UiState>(user ? 'verified' : 'idle');
  const [email, setEmail] = useState(user?.email || '');
  const [otp, setOtp] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [info, setInfo] = useState<string | null>(null);
  const { cartId } = useCart();

  useEffect(() => {
    onVerificationSuccess(uiState === 'verified');
  }, [uiState, onVerificationSuccess]);

  const handleSendVerification = async () => {
    setInfo(null);
    if (!email) {
      setError('Please enter a valid email address.');
      return;
    }
    setIsLoading(true);
    setError(null);
    const formData = new FormData();
    formData.append('email', email);
    const result = await signupOrLogin(undefined, formData);
    if (!result.error) {
      setInfo('Verification code sent! Please check your inbox.');
      setUiState('verifying');
    } else {
      setError(result.error || 'An unexpected error occurred.');
    }
    setIsLoading(false);
  };

  const handleCheckVerification = async (e: React.FormEvent) => {
    setInfo(null);
    e.preventDefault();
    if (!cartId) {
      setError('Cart session not found. Please re-add your item.');
      return;
    }
    if (otp.length !== 6) {
      setError('Please enter the 6-digit code.');
      return;
    }
    setIsLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append('email', email);
    formData.append('otp', otp);
    const result = await loginWithOTP(undefined, formData);
    if (!result.error) {
      setInfo('Email verified!');
      setUiState('verified');
    } else {
      setError(result.error || 'Verification failed.');
    }
    setIsLoading(false);
  };

  return (
    <VStack bg="white" p={6} borderRadius="xl" align="stretch" spacing={6}>
      <Heading as="h2" size="md">
        Contact Information
      </Heading>
      <FormControl isInvalid={!!error && uiState === 'idle'}>
        <FormLabel>Email</FormLabel>
        <HStack>
          <Input
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={e => setEmail(e.target.value)}
            isReadOnly={uiState !== 'idle'}
            onKeyDown={e => {
              if (e.key === 'Enter' && uiState === 'idle' && !user) {
                e.preventDefault();
                handleSendVerification();
              }
            }}
          />
          {uiState === 'idle' && !user && (
            <Button
              bg="#AFFF02"
              color="black"
              _hover={{ bg: '#99e602' }}
              onClick={handleSendVerification}
              isLoading={isLoading}
            >
              Verify
            </Button>
          )}
        </HStack>
        {error && uiState === 'idle' && (
          <FormErrorMessage>{error}</FormErrorMessage>
        )}
        {info && uiState === 'idle' && (
          <Text color="blue.600" fontSize="sm" mt={2}>
            {info}
          </Text>
        )}
      </FormControl>
      {uiState === 'verifying' && (
        <form onSubmit={handleCheckVerification} style={{ width: '100%' }}>
          <VStack align="stretch">
            <FormControl isInvalid={!!error}>
              <FormLabel>Verification Code</FormLabel>
              <HStack>
                <Input
                  placeholder="Enter the 6-digit code"
                  value={otp}
                  onChange={e => {
                    // Only allow digits
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    setOtp(value);
                  }}
                  bg="gray.50"
                  pattern="[0-9]*"
                  inputMode="numeric"
                  maxLength={6}
                  // Allow Enter to submit the form
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.currentTarget.form?.requestSubmit();
                    }
                  }}
                />
                <Button
                  type="submit"
                  bg="#AFFF02"
                  color="black"
                  _hover={{ bg: '#99e602' }}
                  isLoading={isLoading}
                  px={8}
                >
                  Confirm
                </Button>
              </HStack>
              {error && <FormErrorMessage>{error}</FormErrorMessage>}
              {info && (
                <Text color="blue.600" fontSize="sm" mt={2}>
                  {info}
                </Text>
              )}
            </FormControl>
          </VStack>
        </form>
      )}
      {uiState === 'verified' && (
        <HStack color="green.500">
          <Icon as={FiCheckCircle} />
          <Text fontSize="sm">
            Your contact email has been successfully confirmed.
          </Text>
        </HStack>
      )}
      {!user && (
        <Text fontSize="sm" mt={2} textAlign="center">
          Already have an account?{' '}
          <Link
            color="blue.500"
            fontWeight="bold"
            href="/auth/login?url=checkout"
          >
            Login
          </Link>
        </Text>
      )}
    </VStack>
  );
};
