'use client';

import { useActionState, useRef, useEffect } from 'react';
import {
  updateCompanyInfo,
  type CompanyInfoState,
} from '@/app/actions/vendor-auth';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Alert,
  AlertIcon,
  Spinner,
  Textarea,
  Divider,
  Image,
} from '@chakra-ui/react';
import { useState } from 'react';

export default function CompanyInfoForm() {
  const formRef = useRef<HTMLFormElement>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const [formState, formAction, isPending] = useActionState<
    CompanyInfoState,
    FormData
  >(updateCompanyInfo, { error: null, success: false, message: null });

  useEffect(() => {
    if (formState.error) {
      formRef.current?.reset();
    }
  }, [formState]);

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="2xl"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Complete Your Company Profile
          </Heading>

          <Text textAlign="center" color="gray.600" fontSize="sm">
            Add additional information about your company to complete your
            vendor profile
          </Text>

          {formState.error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {formState.error}
            </Alert>
          )}

          <form ref={formRef} action={formAction}>
            <VStack spacing={6}>
              {/* Company Profile Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Company Profile
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="headline" color="gray.600">
                      Company Headline
                    </FormLabel>
                    <Input
                      id="headline"
                      name="headline"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="e.g., Premium Electronics & Gadgets"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="description" color="gray.600">
                      Company Description
                    </FormLabel>
                    <Textarea
                      id="description"
                      name="description"
                      focusBorderColor="primary"
                      placeholder="Tell customers about your company, what you sell, and what makes you unique..."
                      rows={4}
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel htmlFor="logo" color="gray.600">
                      Company Logo
                    </FormLabel>
                    <VStack align="start" spacing={2}>
                      <Input
                        id="logo"
                        name="logo"
                        type="file"
                        accept="image/*"
                        focusBorderColor="primary"
                        disabled={isPending}
                        onChange={handleLogoChange}
                        p={1}
                      />
                      {logoPreview && (
                        <Box>
                          <Text fontSize="sm" color="gray.600" mb={2}>
                            Preview:
                          </Text>
                          <Image
                            src={logoPreview}
                            alt="Logo preview"
                            maxW="100px"
                            maxH="100px"
                            objectFit="contain"
                            borderRadius="md"
                            border="1px solid"
                            borderColor="gray.200"
                          />
                        </Box>
                      )}
                      <Text fontSize="xs" color="gray.500">
                        Upload PNG, JPG, or SVG file (Max 5MB, Recommended:
                        200x200px)
                      </Text>
                    </VStack>
                  </FormControl>
                </VStack>
              </Box>

              <Divider />

              {/* Legal Information Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Legal Information
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="registrationNumber" color="gray.600">
                      Commercial Registration Number
                    </FormLabel>
                    <Input
                      id="registrationNumber"
                      name="registrationNumber"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="Enter your commercial registration number"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="incorporationDate" color="gray.600">
                      Date of Incorporation
                    </FormLabel>
                    <Input
                      id="incorporationDate"
                      name="incorporationDate"
                      type="date"
                      focusBorderColor="primary"
                      disabled={isPending}
                    />
                  </FormControl>
                </VStack>
              </Box>

              <Divider />

              {/* Business Details Section */}
              <Box w="full">
                <Heading size="md" color="gray.700" mb={4}>
                  Business Details
                </Heading>
                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel htmlFor="bankName" color="gray.600">
                      Bank Name
                    </FormLabel>
                    <Input
                      id="bankName"
                      name="bankName"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="e.g., Al Rajhi Bank"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="accountNumber" color="gray.600">
                      Bank Account Number
                    </FormLabel>
                    <Input
                      id="accountNumber"
                      name="accountNumber"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="Enter your bank account number"
                      disabled={isPending}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel htmlFor="iban" color="gray.600">
                      IBAN
                    </FormLabel>
                    <Input
                      id="iban"
                      name="iban"
                      type="text"
                      focusBorderColor="primary"
                      placeholder="************************"
                      disabled={isPending}
                    />
                  </FormControl>
                </VStack>
              </Box>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Complete Profile & Submit for Approval
              </Button>
            </VStack>
          </form>

          <Text textAlign="center" fontSize="xs" color="gray.500">
            Your vendor account will be reviewed by our team before approval.
            You&apos;ll receive an email notification once your account is
            approved.
          </Text>
        </VStack>
      </Box>
    </Flex>
  );
}
