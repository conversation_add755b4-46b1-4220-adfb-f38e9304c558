'use client';
import { HStack, Text, Icon, Box, Flex } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { AiFillFire } from 'react-icons/ai';

// A simple countdown component
const Countdown = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 4,
    minutes: 40,
    seconds: 23,
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      // Dummy logic to decrement time
      setTimeLeft(prev => ({
        ...prev,
        seconds: prev.seconds > 0 ? prev.seconds - 1 : 59,
      }));
    }, 1000);
    return () => clearTimeout(timer);
  }, [timeLeft]);

  const TimeBox = ({ value, label }: { value: number; label: string }) => (
    <Box textAlign="center">
      <Text fontWeight="bold" fontSize="lg">
        {String(value).padStart(2, '0')}
      </Text>
      <Text fontSize="xs">{label}</Text>
    </Box>
  );

  return (
    <HStack spacing={4}>
      <TimeBox value={timeLeft.days} label="Days" />
      <TimeBox value={timeLeft.hours} label="Hours" />
      <TimeBox value={timeLeft.minutes} label="Minutes" />
      <TimeBox value={timeLeft.seconds} label="Seconds" />
    </HStack>
  );
};

const PriceLockBanner = ({ price }: { price: number }) => {
  return (
    <Flex
      bg="#F87171"
      p={4}
      borderRadius="lg"
      align="center"
      justify="space-between"
      color="black"
      direction={{ base: 'column', md: 'row' }}
      gap={4}
    >
      <HStack justify="space-between" gap={2}>
        <Icon as={AiFillFire} boxSize={6} />
        <Text maxW={{ base: '50%', md: 'full' }} fontWeight="bold">
          Lock in this price before it increases to{' '}
        </Text>
        <Box as="span" bg="black" color="white" px={2} py={1} borderRadius="md">
          SAR {price}
        </Box>
      </HStack>
      <Countdown />
    </Flex>
  );
};

export { PriceLockBanner };
