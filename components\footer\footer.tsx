'use client';

import { useCategories } from '@/providers/CategoriesContext';
import {
  Box,
  Container,
  VStack,
  HStack,
  Flex,
  SimpleGrid,
  Text,
  Link,
  Image,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  Divider,
  Icon,
  Circle,
  useBreakpointValue,
  Stack,
} from '@chakra-ui/react';
import {
  FaFacebookF,
  FaLinkedinIn,
  FaInstagram,
  FaPlus,
  FaMinus,
} from 'react-icons/fa';
import { MdOutlineEmail } from 'react-icons/md';

const footerLinks = [
  'About',
  'Careers',
  'Blog',
  'Terms',
  'Privacy',
  'Cookies',
  'FAQ',
];

const socialLinks = [
  { href: '#', icon: FaFacebookF },
  { href: '#', icon: FaLinkedinIn },
  { href: '#', icon: FaInstagram },
];

export const Footer = () => {
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const { categories } = useCategories();

  return (
    <Box as="footer" color="gray.600">
      <Divider />

      <Box bg="gray.50">
        <Container maxW="container.xl" py={{ base: 6, md: 8 }}>
          <Flex
            direction={{ base: 'column', md: 'row' }}
            align="center"
            justify="space-between"
            gap={6}
          >
            <VStack align={{ base: 'center', md: 'start' }} spacing={4}>
              <Text fontWeight="bold" color="gray.800">
                Shop on the go
              </Text>
              <HStack spacing={4}>
                <Link href="#" isExternal>
                  <Image
                    src="/images/apple-store.png"
                    alt="Download on the App Store"
                    h="40px"
                  />
                </Link>
                <Link href="#" isExternal>
                  <Image
                    src="/images/google-store.png"
                    alt="Get it on Google Play"
                    h="40px"
                  />
                </Link>
              </HStack>
            </VStack>
            <VStack align={{ base: 'center', md: 'end' }} spacing={2}>
              <Text fontWeight="bold" color="gray.800">
                We&apos;re Always Here To Help
              </Text>
              <Link
                href="mailto:<EMAIL>"
                _hover={{ textDecoration: 'none' }}
              >
                <HStack
                  border="1px solid"
                  borderColor="gray.300"
                  borderRadius="full"
                  px={4}
                  py={1.5}
                  spacing={2}
                  transition="all 0.2s"
                  _hover={{ bg: 'gray.100', borderColor: 'gray.400' }}
                >
                  <Icon as={MdOutlineEmail} color="gray.500" />
                  <Text fontSize="sm"><EMAIL></Text>
                </HStack>
              </Link>
            </VStack>
          </Flex>
        </Container>
      </Box>

      <Box bg="white">
        <Divider />
        <Container maxW="container.xl" py={{ base: 8, md: 12 }}>
          <VStack spacing={{ base: 8, md: 10 }} align="stretch">
            {isMobile ? (
              <Accordion allowMultiple>
                {categories.map(category => (
                  <AccordionItem
                    key={category.id}
                    borderTop="none"
                    borderBottom="1px solid"
                    borderColor="gray.200"
                  >
                    {({ isExpanded }) => (
                      <>
                        <h2>
                          <AccordionButton py={4}>
                            <Box
                              flex="1"
                              textAlign="left"
                              fontWeight="bold"
                              color="gray.800"
                            >
                              {category.name}
                            </Box>
                            <Icon
                              as={isExpanded ? FaMinus : FaPlus}
                              fontSize="14px"
                              color="gray.600"
                            />
                          </AccordionButton>
                        </h2>
                        <AccordionPanel pb={4} pt={2}>
                          <VStack align="start" spacing={3}>
                            {category.subcategories?.map(sub => (
                              <Link
                                key={sub.id}
                                fontSize="sm"
                                _hover={{ textDecoration: 'underline' }}
                              >
                                {sub.name}
                              </Link>
                            ))}
                          </VStack>
                        </AccordionPanel>
                      </>
                    )}
                  </AccordionItem>
                ))}
              </Accordion>
            ) : (
              <SimpleGrid columns={4} spacingX={8} spacingY={10}>
                {categories.map(category => (
                  <VStack key={category.id} align="start" spacing={4}>
                    <Text fontWeight="bold" color="gray.800">
                      {category.name}
                    </Text>
                    <VStack align="start" spacing={2}>
                      {category.subcategories?.map(sub => (
                        <Link
                          key={sub.id}
                          fontSize="sm"
                          /** for now using sub.name
                           * later we update it to use
                           * sub.slug for beter seo
                           * need to update category table
                           * either manually or writing an sql script
                           * */
                          href={`category/${sub.name}`}
                          _hover={{ textDecoration: 'underline' }}
                        >
                          {sub.name}
                        </Link>
                      ))}
                    </VStack>
                  </VStack>
                ))}
              </SimpleGrid>
            )}

            <Divider />

            <Flex
              direction={{ base: 'column', md: 'row' }}
              align="center"
              justify="space-between"
              gap={6}
            >
              <Stack
                direction={{ base: 'column', lg: 'row' }}
                align="center"
                spacing={{ base: 4, lg: 6 }}
              >
                <Text fontSize="sm" textAlign="center" whiteSpace="nowrap">
                  © 2025 Collab. All Rights Reserved
                </Text>
                <HStack
                  spacing={{ base: 4, md: 6 }}
                  wrap="wrap"
                  justify="center"
                >
                  {footerLinks.map(link => (
                    <Link
                      key={link}
                      fontSize="sm"
                      _hover={{ textDecoration: 'underline' }}
                    >
                      {link}
                    </Link>
                  ))}
                </HStack>
              </Stack>
              <VStack align={{ base: 'center', md: 'end' }} spacing={3}>
                <Text fontWeight="bold" color="gray.800" fontSize="sm">
                  CONNECT WITH US
                </Text>
                <HStack spacing={3}>
                  {socialLinks.map((social, index) => (
                    <Link key={index} href={social.href} isExternal>
                      <Circle
                        size="36px"
                        bg="#c6f621"
                        transition="transform 0.2s"
                        _hover={{ transform: 'scale(1.1)' }}
                      >
                        <Icon as={social.icon} color="black" boxSize="18px" />
                      </Circle>
                    </Link>
                  ))}
                </HStack>
              </VStack>
            </Flex>
          </VStack>
        </Container>
      </Box>
    </Box>
  );
};

export default Footer;
