import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import CompanyInfoForm from './_components/CompanyInfoForm';

export default async function CompanyInfoPage() {
  const supabase = await createClient();

  // Check if user is logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/vendor/auth/login');
  }

  // Check if user's email is verified
  if (!user.email_confirmed_at) {
    redirect('/vendor/auth/login');
  }

  // Check if user already has a vendor account
  const { data: vendor } = await supabase
    .from('vendor')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (!vendor) {
    redirect('/vendor/auth/signup');
  }

  // Check if company info is already complete - all required fields
  if (vendor.headline && vendor.description && vendor.date_of_incorporation) {
    redirect('/vendor/dashboard');
  }

  return <CompanyInfoForm />;
}
