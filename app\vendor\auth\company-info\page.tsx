import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import CompanyInfoForm from './_components/CompanyInfoForm';

export default async function CompanyInfoPage() {
  const supabase = await createClient();

  // Check if user is logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/vendor/auth/login');
  }

  // Check if user already has a vendor account
  const { data: vendor } = await supabase
    .from('vendor')
    .select('*')
    .eq('user_id', user.id)
    .single();

  if (!vendor) {
    redirect('/vendor/auth/signup');
  }

  // Email verification is handled by Supabase auth system
  // No need to check vendor status for email verification

  // Check if company info is already complete
  if (vendor.headline && vendor.description) {
    redirect('/vendor/dashboard');
  }

  return <CompanyInfoForm />;
}
