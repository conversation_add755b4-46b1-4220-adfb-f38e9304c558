'use client';
import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  Icon,
  Input,
  InputGroup,
  InputRightElement,
  VStack,
} from '@chakra-ui/react';
import { FaCcVisa, FaCcMastercard, FaCreditCard } from 'react-icons/fa';

/**
 * A shared type definition for card details.
 */
type CardDetails = {
  name: string;
  number: string;
  month: string;
  year: string;
  cvc: string;
};

/**
 * This type now correctly defines the props that the parent (PaymentPanel)
 * will pass down to control this component's state and display errors.
 */
type MoyasarCustomFormProps = {
  cardDetails: CardDetails;
  setCardDetails: (details: CardDetails) => void;
  errors: Record<string, string[]>;
};

/**
 * A utility to determine the card brand from its number.
 */
const getCardBrand = (cardNumber: string) => {
  if (cardNumber.startsWith('4')) {
    return { brand: 'Visa', icon: FaCcVisa };
  }
  if (cardNumber.startsWith('5')) {
    return { brand: 'Mastercard', icon: FaCcMastercard };
  }
  return { brand: 'Card', icon: FaCreditCard };
};

/**
 * A "dumb" presentational component for the credit card form.
 * It receives its state and error messages via props and reports
 * user input back up to its parent.
 */
export const MoyasarCustomForm = ({
  cardDetails,
  setCardDetails,
  errors,
}: MoyasarCustomFormProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardDetails({ ...cardDetails, [e.target.name]: e.target.value });
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/\D/g, '');

    if (value.length > 2) {
      value = value.slice(0, 2) + ' / ' + value.slice(2, 4);
    }

    const [month, year] = value.split(' / ');

    setCardDetails({
      ...cardDetails,
      month: month || '',
      year: year || '', // We assume a 2-digit year
    });
  };

  const { icon: BrandIcon } = getCardBrand(cardDetails.number);

  return (
    <VStack as="div" spacing={4} align="stretch">
      <FormControl isInvalid={!!errors.name}>
        <FormLabel fontSize="sm">Name on Card</FormLabel>
        <Input
          name="name"
          value={cardDetails.name}
          onChange={handleChange}
          bg="gray.50"
        />
        {errors.name && <FormErrorMessage>{errors.name[0]}</FormErrorMessage>}
      </FormControl>

      <FormControl isInvalid={!!errors.number}>
        <FormLabel fontSize="sm">Card Number</FormLabel>
        <InputGroup>
          <Input
            name="number"
            value={cardDetails.number}
            onChange={handleChange}
            bg="gray.50"
          />
          <InputRightElement>
            <Icon as={BrandIcon} color="gray.400" />
          </InputRightElement>
        </InputGroup>
        {errors.number && (
          <FormErrorMessage>{errors.number[0]}</FormErrorMessage>
        )}
      </FormControl>

      <Grid templateColumns="repeat(2, 1fr)" gap={4}>
        <FormControl isInvalid={!!errors.month || !!errors.year}>
          <FormLabel fontSize="sm">Expiry</FormLabel>
          <Input
            placeholder="MM / YY"
            value={`${cardDetails.month}${cardDetails.year ? ' / ' + cardDetails.year : ''}`}
            onChange={handleExpiryChange}
            bg="gray.50"
          />
          {(errors.month || errors.year) && (
            <FormErrorMessage>
              {(errors.month || errors.year)?.[0]}
            </FormErrorMessage>
          )}
        </FormControl>
        <FormControl isInvalid={!!errors.cvc}>
          <FormLabel fontSize="sm">CVC</FormLabel>
          <Input
            name="cvc"
            value={cardDetails.cvc}
            onChange={handleChange}
            bg="gray.50"
          />
          {errors.cvc && <FormErrorMessage>{errors.cvc[0]}</FormErrorMessage>}
        </FormControl>
      </Grid>
    </VStack>
  );
};
