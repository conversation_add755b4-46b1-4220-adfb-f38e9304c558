import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    mockReset: true,
    coverage: {
      //provider: "v8",
      include: ['**/components/**'],
      exclude: ['**/components/**/index.{ts,tsx}'],
    },
    environment: 'jsdom',
  },
});
