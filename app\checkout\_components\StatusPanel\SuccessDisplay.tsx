'use client';
import {
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  Grid,
  GridItem,
  Box,
} from '@chakra-ui/react';
import { FiCheckCircle } from 'react-icons/fi';
import { useCart } from '@/providers/CartContext';
import { createClient } from '@/utils/supabase/client';
import { useEffect, useState } from 'react';
import type { User } from '@supabase/supabase-js';
import { ShippingAddressPanel } from './ShippingAddressPanel';
import { ReviewOrderPanel } from './ReviewOrderPanel';

export const SuccessDisplay = () => {
  const { item: cartItem, removeItem } = useCart();
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const supabase = createClient();
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user);
    });
  }, []);

  if (!cartItem) {
    return (
      <VStack spacing={4} p={10}>
        <Icon as={FiCheckCircle} boxSize={16} color="green.500" />
        <Heading as="h2" size="lg">
          Item Removed
        </Heading>
        <Text color="gray.600">Your cart is now empty.</Text>
      </VStack>
    );
  }

  return (
    <VStack
      align="stretch"
      spacing={6}
      w="full"
      maxW="container.xl"
      minH="100%"
      mx="auto"
    >
      <HStack
        bg="green.50"
        p={4}
        borderRadius="xl"
        borderWidth="1px"
        borderColor="green.200"
      >
        <Icon as={FiCheckCircle} boxSize={6} color="green.500" />
        <Box>
          <Text fontWeight="bold">Your order is confirmed</Text>
          {user?.email && (
            <Text fontSize="sm" color="gray.600">
              A confirmation email has been sent to {user.email}
            </Text>
          )}
        </Box>
      </HStack>

      {/* Main Content Grid */}
      <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={8}>
        <GridItem>
          <ShippingAddressPanel />
        </GridItem>
        <GridItem>
          <ReviewOrderPanel item={cartItem} onRemove={removeItem} />
        </GridItem>
      </Grid>
    </VStack>
  );
};
