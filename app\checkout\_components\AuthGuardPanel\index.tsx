'use client';
import { Grid, GridItem, VStack, Box } from '@chakra-ui/react';
import type { User } from '@supabase/supabase-js';
import type { useCart } from '@/providers/CartContext';
import { Header } from './Header';
import { PriceLockBanner } from './PriceLockBanner';
import { ContactForm } from './ContactForm';
import { OrderSummary } from './OrderSummary';
import { PaymentPanel } from './PaymentPanel';
import { useState } from 'react';

type AuthGuardPanelProps = {
  user: User | null;
  cart: ReturnType<typeof useCart>;
  onBack: () => void;
};

export const AuthGuardPanel = ({ user, cart, onBack }: AuthGuardPanelProps) => {
  // State to track if contact info is verified ---
  const [isContactVerified, setIsContactVerified] = useState(!!user);

  if (!cart.item) {
    return <div>Your cart is empty.</div>;
  }

  const summaryData = {
    subtotal: cart.cartTotal,
    shippingFee: 'Free' as const,
    total: cart.cartTotal,
  };

  return (
    <VStack minH="100vh" align="stretch" spacing={0}>
      <Header onBack={onBack} />
      <VStack
        w="full"
        maxW="container.xl"
        mx="auto"
        p={{ base: 4, md: 8 }}
        align="stretch"
        spacing={{ base: 6, lg: 8 }}
      >
        <PriceLockBanner price={139} />
        <Grid
          templateColumns={{ base: '1fr', lg: '1fr 0.6fr' }}
          gap={{ base: 6, lg: 8 }}
        >
          <GridItem as={VStack} align="stretch" spacing={6}>
            <ContactForm
              user={user}
              onVerificationSuccess={setIsContactVerified}
            />
            <Box
              opacity={isContactVerified ? 1 : 0.5}
              pointerEvents={isContactVerified ? 'auto' : 'none'}
              w="full"
            >
              <PaymentPanel user={user} />
            </Box>
          </GridItem>
          <GridItem>
            <OrderSummary summary={summaryData} />
          </GridItem>
        </Grid>
      </VStack>
    </VStack>
  );
};
