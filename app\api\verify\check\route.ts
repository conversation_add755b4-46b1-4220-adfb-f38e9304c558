import { NextResponse } from 'next/server';

import twilio from 'twilio';

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);
const verifyServiceSid = process.env.TWILIO_VERIFY_SERVICE_SID as string;

export async function POST(request: Request) {
  const { phone, code } = await request.json();

  if (!phone || !code) {
    return NextResponse.json(
      { error: 'Phone number and code are required.' },
      { status: 400 }
    );
  }

  try {
    const verificationCheck = await twilioClient.verify.v2
      .services(verifyServiceSid)
      .verificationChecks.create({ to: phone, code });

    if (verificationCheck.status === 'approved') {
      // Update user_profile uppon successful verification
      /*
       * 
       if (user) {
        const { error: dbError } = await supabase
        .from('user_profile')
        .update({
          phone: phone,
          isPhoneVerified: true,
          updated_at: new Date().toISOString(), // Good practice to update this timestamp
        })
        .eq('id', user.id); // The WHERE clause to update the correct user

      if (dbError) {
        // This will catch errors like the profile not existing or RLS issues.
        console.error('Supabase update error:', dbError);
        throw new Error(`Database error: ${dbError.message}`);
      }} */

      return NextResponse.json({
        success: true,
        message: 'Phone number verified and profile updated.',
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid verification code.' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Verification check process error:', error);
    return NextResponse.json(
      { error: 'Failed to verify code.', details: `${error}` },
      { status: 500 }
    );
  }
}
