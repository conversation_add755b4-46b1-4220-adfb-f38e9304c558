import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/providers/AuthContext';
import { Footer } from '@/components/footer';
import { ThemeProvider } from '@/providers/chakra-provider';
import { CartProvider } from '@/providers/CartContext';
import { NavBar } from '@/components/navbar';
import ProfileButton from '@/components/navbar/profile-button';
import { CategoriesProvider } from '@/providers/CategoriesContext';
import { Suspense } from 'react';
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <link
          rel="stylesheet"
          href="https://unpkg.com/moyasar-payment-form@2.0.16/dist/moyasar.css"
        />
        <script
          src="https://unpkg.com/moyasar-payment-form@2.0.16/dist/moyasar.umd.js"
          defer // Use defer to ensure it's executed after the document is parsed
        ></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-50`}
      >
        <Suspense>
          <ThemeProvider>
            <AuthProvider>
              <CategoriesProvider>
                <CartProvider>
                  <main>
                    <NavBar authButton={<ProfileButton />} />
                    {children}
                  </main>
                </CartProvider>
                <Footer />
              </CategoriesProvider>
            </AuthProvider>
          </ThemeProvider>
        </Suspense>
      </body>
    </html>
  );
}
