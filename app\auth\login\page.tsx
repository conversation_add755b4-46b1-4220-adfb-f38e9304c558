import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import LoginForm from './_components/login-form';

export default async function LoginPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect('/');
  }

  return <LoginForm />;
}
