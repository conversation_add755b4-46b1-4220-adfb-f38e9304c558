import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorSignupForm from './_components/VendorSignupForm';

export default async function VendorSignupPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    // Check vendor status and redirect accordingly
    const { data: vendor } = await supabase
      .from('vendor')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (vendor) {
      // Check if company info is complete
      if (vendor.headline && vendor.description && vendor.date_of_incorporation) {
        redirect('/vendor/dashboard');
      } else {
        redirect('/vendor/auth/company-info');
      }
    } else {
      // User exists but no vendor record, stay on signup page
      // This allows them to complete vendor signup
    }
  }

  return <VendorSignupForm />;
}
