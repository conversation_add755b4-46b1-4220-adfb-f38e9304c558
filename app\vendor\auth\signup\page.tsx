import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorSignupForm from './_components/VendorSignupForm';

export default async function VendorSignupPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect('/vendor/dashboard');
  }

  return <VendorSignupForm />;
}
