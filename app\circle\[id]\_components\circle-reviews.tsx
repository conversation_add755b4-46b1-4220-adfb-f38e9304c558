'use client';

import React, { useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  GridItem,
  Heading,
  HStack,
  Icon,
  Link,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Progress,
  Text,
  VStack,
} from '@chakra-ui/react';
import { FaStar } from 'react-icons/fa';
import { FiChevronDown } from 'react-icons/fi';

// =========================================================================
// === DUMMY DATA & TYPE DEFINITIONS                                     ===
// =========================================================================

interface RatingDistributionItem {
  star: number;
  percentage: number;
}

interface Review {
  id: number;
  author: string;
  avatarUrl: string;
  date: string;
  rating: number;
  text: string;
}

interface CircleReviewsData {
  overallRating: number;
  totalReviews: number;
  ratingDistribution: RatingDistributionItem[];
  reviews: Review[];
}

// In a real app, this data would come from an API. We pass it in as a prop.
const DUMMY_REVIEWS_DATA: CircleReviewsData = {
  overallRating: 4.9,
  totalReviews: 1908,
  ratingDistribution: [
    { star: 5, percentage: 60 },
    { star: 4, percentage: 16 },
    { star: 3, percentage: 9 },
    { star: 2, percentage: 4 },
    { star: 1, percentage: 12 },
  ],
  reviews: [
    {
      id: 1,
      author: 'Ahmed K.',
      avatarUrl: 'https://bit.ly/dan-abramov',
      date: 'Apr 25, 2025',
      rating: 5,
      text: 'Smooth experience! Got my order on time and paid less. Love it! This is some extra text to demonstrate the truncation feature, ensuring that longer reviews are handled gracefully by the UI without breaking the layout.',
    },
    {
      id: 2,
      author: 'Lina B.',
      avatarUrl: 'https://bit.ly/tioluwani-kolawole',
      date: 'Apr 25, 2025',
      rating: 5,
      text: 'Worth waiting a few days to save almost 100 riyals. The product was exactly as described and the process was seamless.',
    },
    {
      id: 3,
      author: 'Lina B.',
      avatarUrl: 'https://bit.ly/ryan-florence',
      date: 'Apr 25, 2025',
      rating: 4,
      text: 'Good deal, but shipping could be a bit faster. Overall satisfied.',
    },
  ],
};

// =========================================================================
// === SUB-COMPONENTS for a clean and reusable architecture              ===
// =========================================================================

// A generic star rating display component.
const StarRating = ({
  rating,
  color = 'green.400',
}: {
  rating: number;
  color?: string;
}) => (
  <HStack spacing={1}>
    {[...Array(5)].map((_, i) => (
      <Icon key={i} as={FaStar} color={i < rating ? color : 'gray.300'} />
    ))}
  </HStack>
);

// A component for the individual rating breakdown bars.
const RatingBar = ({
  starLevel,
  percentage,
}: {
  starLevel: number;
  percentage: number;
}) => {
  const barColors: { [key: number]: string } = {
    5: 'green',
    4: 'teal',
    3: 'yellow',
    2: 'orange',
    1: 'red',
  };

  return (
    <HStack w="full" spacing={3} maxW="container.xl" mx="auto">
      <Text fontSize="sm" fontWeight="medium" color="gray.700" w="20px">
        {starLevel}
      </Text>
      <Progress
        value={percentage}
        colorScheme={barColors[starLevel]}
        bg="gray.200"
        borderRadius="full"
        size="sm"
        flex="1"
      />
      <Text
        fontSize="sm"
        fontWeight="bold"
        color="gray.800"
        w="40px"
        textAlign="right"
      >
        {percentage}%
      </Text>
    </HStack>
  );
};

// A self-contained card for a single review, with its own state for text expansion.
const ReviewCard = ({ review }: { review: Review }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const isTruncated = review.text.length > 130;

  return (
    <VStack align="stretch" spacing={3}>
      <HStack spacing={4}>
        <Avatar name={review.author} size="md" />
        <VStack align="start" spacing={0}>
          <Text fontWeight="bold">{review.author}</Text>
          <Text fontSize="xs" color="gray.500">
            {review.date}
          </Text>
        </VStack>
      </HStack>
      <StarRating rating={review.rating} />
      <Text color="gray.700" fontSize="sm">
        {isTruncated && !isExpanded
          ? `${review.text.substring(0, 130)}... `
          : review.text}
        {isTruncated && !isExpanded && (
          <Link
            color="blue.500"
            fontWeight="medium"
            onClick={() => setIsExpanded(true)}
          >
            More
          </Link>
        )}
      </Text>
    </VStack>
  );
};

// =========================================================================
// === MAIN COMPONENT                                                    ===
// =========================================================================
const CircleReviews = ({ data }: { data: CircleReviewsData }) => {
  return (
    <Box
      bg="white"
      p={{ base: 4, md: 8 }}
      borderRadius="xl"
      maxW="container.xl"
      mx="auto"
    >
      <VStack align="stretch" spacing={6}>
        <Heading as="h2" size="lg" fontWeight="bold">
          Circle Ratings & Reviews
        </Heading>
        <Divider />
        <Grid
          templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
          gap={{ base: 8, lg: 16 }}
        >
          {/* --- LEFT COLUMN: Overall Rating Summary --- */}
          <GridItem colSpan={{ base: 1, lg: 1 }}>
            <VStack align="start" spacing={2}>
              <Text fontWeight="bold" fontSize="lg">
                Overall Rating
              </Text>
              <Text fontSize="5xl" fontWeight="bold" lineHeight="1">
                {data.overallRating}
              </Text>
              <StarRating rating={data.overallRating} />
              <Text fontSize="sm" color="gray.500">
                Based on {data.totalReviews} overall rating
              </Text>
              <VStack align="stretch" spacing={1} w="full" pt={4}>
                {data.ratingDistribution.map(item => (
                  <RatingBar
                    key={item.star}
                    starLevel={item.star}
                    percentage={item.percentage}
                  />
                ))}
              </VStack>
            </VStack>
          </GridItem>

          {/* --- RIGHT COLUMN: Reviews List --- */}
          <GridItem colSpan={{ base: 1, lg: 2 }}>
            <VStack align="stretch" spacing={6}>
              <Flex justify="space-between" align="center">
                <Text fontWeight="bold" fontSize="lg">
                  {data.totalReviews} Reviews
                </Text>
                <Menu>
                  <MenuButton
                    as={Button}
                    rightIcon={<FiChevronDown />}
                    size="sm"
                    variant="outline"
                  >
                    All
                  </MenuButton>
                  <MenuList>
                    <MenuItem>All</MenuItem>
                    <MenuItem>Most Recent</MenuItem>
                    <MenuItem>Highest Rated</MenuItem>
                    <MenuItem>Lowest Rated</MenuItem>
                  </MenuList>
                </Menu>
              </Flex>
              <VStack align="stretch" spacing={6} divider={<Divider />}>
                {data.reviews.map(review => (
                  <ReviewCard key={review.id} review={review} />
                ))}
              </VStack>{' '}
              {/* Fixed typo here: VVStack -> VStack */}
              <Flex justify="center" pt={4}>
                <Button
                  variant="outline"
                  colorScheme="gray"
                  fontWeight="semibold"
                >
                  See all reviews
                </Button>
              </Flex>
            </VStack>
          </GridItem>
        </Grid>
      </VStack>
    </Box>
  );
};

// Example of how to use the component by passing it the dummy data.
const ReviewsSection = () => {
  return <CircleReviews data={DUMMY_REVIEWS_DATA} />;
};

export default ReviewsSection; // Exporting the wrapper for easy use.
