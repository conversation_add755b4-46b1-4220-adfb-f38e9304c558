'use client';
import { createClient } from '@/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import React, { createContext, useState, useEffect, ReactNode } from 'react';

interface AuthContextProps {
  user: User | null;
  loading: boolean;
  error: string | null;
}

const Context = createContext<AuthContextProps>({
  user: null,
  loading: false,
  error: null,
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Create the client once
    const supabase = createClient();

    // Initial auth check
    const getInitialSession = async () => {
      try {
        const { data } = await supabase.auth.getUser();
        setUser(data?.user || null);
      } catch (err) {
        console.error(err);
        setError(`Failed to fetch user`);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user || null);
    });

    // Clean up subscription when component unmounts
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const value = { user, loading, error };

  return <Context.Provider value={value}>{children}</Context.Provider>;
};

export const useAuthContext = () => {
  return React.useContext(Context);
};
