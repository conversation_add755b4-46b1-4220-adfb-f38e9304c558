import NextLink from 'next/link';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Menu<PERSON>ist,
  MenuItem,
  MenuDivider,
  Avatar,
  IconButton,
  Link as ChakraLink,
} from '@chakra-ui/react';
import { HiOutlineUser } from 'react-icons/hi2';
import { Fi<PERSON>ogOut, FiUser, FiSettings } from 'react-icons/fi';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

const ProfileButton = async () => {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Add revalidation on authentication changes
  const signOut = async () => {
    'use server';
    const supabase = await createClient();
    await supabase.auth.signOut();
    revalidatePath('/');
  };

  if (user) {
    const avatarUrl = (user.user_metadata?.avatar_url as string) || '';

    return (
      <Menu>
        <MenuButton
          as={IconButton}
          aria-label="User menu"
          icon={<Avatar size="sm" name={user.email || ''} src={avatarUrl} />}
          variant="ghost"
          isRound
          _hover={{ bg: 'rgba(0,0,0,0.1)' }}
          _active={{ bg: 'rgba(0,0,0,0.2)' }}
        />
        <MenuList bg="white" color="gray.800" zIndex="popover">
          <MenuItem
            as={NextLink}
            href="/profile"
            icon={<FiUser />}
            _hover={{ bg: 'gray.100' }}
          >
            My Profile
          </MenuItem>
          <MenuItem
            as={NextLink}
            href="/settings"
            icon={<FiSettings />}
            _hover={{ bg: 'gray.100' }}
          >
            Settings
          </MenuItem>
          <MenuDivider />
          <form action={signOut}>
            <MenuItem
              as="button"
              type="submit"
              icon={<FiLogOut />}
              color="red.500"
              _hover={{ bg: 'red.50' }}
              w="full"
            >
              Logout
            </MenuItem>
          </form>
        </MenuList>
      </Menu>
    );
  }

  // If no user
  return (
    <ChakraLink as={NextLink} href="/auth/login" _hover={{}}>
      <IconButton
        aria-label="Login"
        icon={<HiOutlineUser size={25} />}
        variant="ghost"
        fontSize="lg"
        _hover={{ bg: 'rgba(0,0,0,0.1)' }}
      />
    </ChakraLink>
  );
};

export default ProfileButton;
