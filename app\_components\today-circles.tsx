'use client';

import { CSSProperties, useState } from 'react'; // useEffect is no longer needed
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import {
  Box,
  Flex,
  Spacer,
  Heading,
  Text,
  Link as ChakraLink,
  Spinner,
} from '@chakra-ui/react';
import Link from 'next/link';
import { FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { CircleCard } from '@/components/cards';
import { Circle } from '@/utils/supabase/queries';

interface TodayCirclesProps {
  circles: Circle[];
}

const TodayCircles = ({ circles }: TodayCirclesProps) => {
  const [isSwiperReady, setIsSwiperReady] = useState(false);

  if (!circles || circles.length === 0) {
    return null;
  }

  return (
    <Box p={{ base: 4, md: 8 }}>
      <Box
        as="section"
        bg="white"
        py={6}
        px={{ base: 2, sm: 4, md: 6 }}
        maxW="container.xl"
        mx="auto"
        borderRadius="xl"
        boxShadow="base"
      >
        <Flex align="center" mb={6} px={{ base: 2, md: 0 }} gap={1}>
          <Heading
            as="h2"
            fontSize={{ base: 'md', md: '2xl' }}
            minW="fit-content"
            fontWeight="bold"
          >
            {"Today's Sale Circles"}
          </Heading>
          <Spacer />
          <ChakraLink
            as={Link}
            href="/circles"
            border="1px"
            borderColor="gray.300"
            px={{ base: 3, md: 4 }}
            py={2}
            borderRadius="lg"
            _hover={{ textDecoration: 'none', bg: 'gray.50' }}
            display="flex"
            alignItems="center"
            gap={1}
            minW={{ base: '7rem', md: '10rem' }}
          >
            <Text fontSize={{ base: '2xs', md: 'md' }} fontWeight="medium">
              View All Circles
            </Text>
            <FiChevronRight size={16} />
          </ChakraLink>
        </Flex>

        <Box position="relative" mx={{ base: -2, sm: -4, md: -6 }} minH="full">
          {!isSwiperReady && (
            <Flex
              align="center"
              justify="center"
              h="450px"
              position="absolute"
              w="full"
            >
              <Spinner
                size="xl"
                thickness="4px"
                speed="0.65s"
                emptyColor="gray.200"
                color="primary"
              />
            </Flex>
          )}

          <Box
            opacity={isSwiperReady ? 1 : 0}
            transition="opacity 0.4s ease-in-out"
          >
            <Swiper
              onSwiper={() => setIsSwiperReady(true)}
              modules={[Navigation]}
              spaceBetween={16}
              slidesPerView={'auto'}
              breakpoints={{
                320: { slidesPerView: 1.15, spaceBetween: 12 },
                640: { slidesPerView: 2.2, spaceBetween: 16 },
                768: { slidesPerView: 2.5, spaceBetween: 16 },
                1024: { slidesPerView: 3.5, spaceBetween: 20 },
                1280: { slidesPerView: 4.5, spaceBetween: 20 },
              }}
              navigation={{
                nextEl: '.swiper-button-next-custom',
                prevEl: '.swiper-button-prev-custom',
              }}
              style={
                { paddingLeft: '24px', paddingRight: '24px' } as CSSProperties
              }
            >
              {circles.map(circle => (
                <SwiperSlide key={circle.id} style={{ height: 'auto' }}>
                  <CircleCard circle={circle} />
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>

          <Flex
            className="swiper-button-prev-custom"
            align="center"
            justify="center"
            position="absolute"
            left={{ base: 1, md: 0 }}
            top="50%"
            zIndex={10}
            transform="translateY(-50%)"
            display={{ base: 'none', md: 'flex' }}
            w="44px"
            h="44px"
            borderRadius="full"
            bg="white"
            cursor="pointer"
            boxShadow="md"
            border="1px"
            borderColor="gray.200"
            _hover={{ bg: 'gray.50' }}
          >
            <FiChevronLeft size={24} />
          </Flex>
          <Flex
            className="swiper-button-next-custom"
            align="center"
            justify="center"
            position="absolute"
            right={{ base: 1, md: 0 }}
            top="50%"
            zIndex={10}
            transform="translateY(-50%)"
            display={{ base: 'none', md: 'flex' }}
            w="44px"
            h="44px"
            borderRadius="full"
            bg="white"
            cursor="pointer"
            boxShadow="md"
            border="1px"
            borderColor="gray.200"
            _hover={{ bg: 'gray.50' }}
          >
            <FiChevronRight size={24} />
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default TodayCircles;
