import type { PricingTier } from '@/utils/supabase/queries';

export function getPrice(
  pricing_tiers: PricingTier[],
  sale_price: number,
  current_participants: number
) {
  const sortedTiers = [...pricing_tiers].sort(
    (a, b) => (a.threshold || 0) - (b.threshold || 0)
  );
  const upcomingTier = sortedTiers.find(
    tier => (tier.threshold || 0) > current_participants!
  );
  const lastAchievedTier = sortedTiers
    .filter(tier => (tier.threshold || 0) < current_participants!)
    .at(-1);
  const price = upcomingTier?.price ?? lastAchievedTier?.price ?? sale_price;
  const nextTier = sortedTiers.find(tier => tier.price > Number(price));
  const tierChangeAt = upcomingTier?.threshold;
  return { price, nextTier, tierChangeAt };
}
