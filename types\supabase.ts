export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)';
  };
  public: {
    Tables: {
      cart: {
        Row: {
          circle_id: number | null;
          created_at: string | null;
          id: string;
          quantity: number;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: string;
          quantity?: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: string;
          quantity?: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'cart_circle_id_fkey';
            columns: ['circle_id'];
            isOneToOne: false;
            referencedRelation: 'circle';
            referencedColumns: ['id'];
          },
        ];
      };
      category: {
        Row: {
          category_image: string | null;
          created_at: string | null;
          id: string;
          name: string;
          parent: string | null;
          slug: string;
        };
        Insert: {
          category_image?: string | null;
          created_at?: string | null;
          id: string;
          name: string;
          parent?: string | null;
          slug: string;
        };
        Update: {
          category_image?: string | null;
          created_at?: string | null;
          id?: string;
          name?: string;
          parent?: string | null;
          slug?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'category_parent_fkey';
            columns: ['parent'];
            isOneToOne: false;
            referencedRelation: 'category';
            referencedColumns: ['id'];
          },
        ];
      };
      circle: {
        Row: {
          circle_type: Database['public']['Enums']['circle_type'] | null;
          created_at: string | null;
          current_participants: number | null;
          description: string | null;
          end_date: string;
          id: number;
          max_participants: number;
          max_quantity: number;
          min_quantity: number;
          pricing_tiers: Json | null;
          product_id: string | null;
          sale_price: number;
          start_date: string;
          status: Database['public']['Enums']['circle_status_enum'];
          updated_at: string | null;
          vendor_id: string | null;
        };
        Insert: {
          circle_type?: Database['public']['Enums']['circle_type'] | null;
          created_at?: string | null;
          current_participants?: number | null;
          description?: string | null;
          end_date: string;
          id?: number;
          max_participants: number;
          max_quantity?: number;
          min_quantity?: number;
          pricing_tiers?: Json | null;
          product_id?: string | null;
          sale_price: number;
          start_date: string;
          status?: Database['public']['Enums']['circle_status_enum'];
          updated_at?: string | null;
          vendor_id?: string | null;
        };
        Update: {
          circle_type?: Database['public']['Enums']['circle_type'] | null;
          created_at?: string | null;
          current_participants?: number | null;
          description?: string | null;
          end_date?: string;
          id?: number;
          max_participants?: number;
          max_quantity?: number;
          min_quantity?: number;
          pricing_tiers?: Json | null;
          product_id?: string | null;
          sale_price?: number;
          start_date?: string;
          status?: Database['public']['Enums']['circle_status_enum'];
          updated_at?: string | null;
          vendor_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'circle_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'product';
            referencedColumns: ['csin'];
          },
          {
            foreignKeyName: 'circle_vendor_id_fkey';
            columns: ['vendor_id'];
            isOneToOne: false;
            referencedRelation: 'vendor';
            referencedColumns: ['id'];
          },
        ];
      };
      flash_circles: {
        Row: {
          circle_id: number | null;
          created_at: string | null;
          id: number;
        };
        Insert: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: number;
        };
        Update: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'flash_circles_circle_id_fkey';
            columns: ['circle_id'];
            isOneToOne: false;
            referencedRelation: 'circle';
            referencedColumns: ['id'];
          },
        ];
      };
      order: {
        Row: {
          circle_id: number | null;
          created_at: string | null;
          id: string;
          payment_method: string | null;
          payment_status: string | null;
          quantity: number;
          shipping_address: Json | null;
          status: string | null;
          total_price: number;
          unit_price: number;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: string;
          payment_method?: string | null;
          payment_status?: string | null;
          quantity: number;
          shipping_address?: Json | null;
          status?: string | null;
          total_price: number;
          unit_price: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: string;
          payment_method?: string | null;
          payment_status?: string | null;
          quantity?: number;
          shipping_address?: Json | null;
          status?: string | null;
          total_price?: number;
          unit_price?: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'order_circle_id_fkey';
            columns: ['circle_id'];
            isOneToOne: false;
            referencedRelation: 'circle';
            referencedColumns: ['id'];
          },
        ];
      };
      product: {
        Row: {
          about: string | null;
          brand: string;
          category_1: string | null;
          category_2: string | null;
          country_of_origin: string;
          created_at: string | null;
          csin: string;
          currency: string | null;
          description: string;
          dimension_height_in_cm: number;
          dimension_length_in_cm: number;
          dimension_width_in_cm: number;
          main_category: string | null;
          market_price: number;
          meta_description: string | null;
          meta_keywords: string[] | null;
          meta_title: string | null;
          model: string;
          name: string;
          product_image: string | null;
          search_vector: unknown | null;
          sku: string;
          slug: string;
          status: Database['public']['Enums']['product_status_enum'];
          title: string;
          upc: string;
          updated_at: string | null;
          warranty: string | null;
          weight_in_kg: number;
        };
        Insert: {
          about?: string | null;
          brand: string;
          category_1?: string | null;
          category_2?: string | null;
          country_of_origin: string;
          created_at?: string | null;
          csin: string;
          currency?: string | null;
          description: string;
          dimension_height_in_cm: number;
          dimension_length_in_cm: number;
          dimension_width_in_cm: number;
          main_category?: string | null;
          market_price: number;
          meta_description?: string | null;
          meta_keywords?: string[] | null;
          meta_title?: string | null;
          model: string;
          name: string;
          product_image?: string | null;
          search_vector?: unknown | null;
          sku: string;
          slug: string;
          status?: Database['public']['Enums']['product_status_enum'];
          title: string;
          upc: string;
          updated_at?: string | null;
          warranty?: string | null;
          weight_in_kg: number;
        };
        Update: {
          about?: string | null;
          brand?: string;
          category_1?: string | null;
          category_2?: string | null;
          country_of_origin?: string;
          created_at?: string | null;
          csin?: string;
          currency?: string | null;
          description?: string;
          dimension_height_in_cm?: number;
          dimension_length_in_cm?: number;
          dimension_width_in_cm?: number;
          main_category?: string | null;
          market_price?: number;
          meta_description?: string | null;
          meta_keywords?: string[] | null;
          meta_title?: string | null;
          model?: string;
          name?: string;
          product_image?: string | null;
          search_vector?: unknown | null;
          sku?: string;
          slug?: string;
          status?: Database['public']['Enums']['product_status_enum'];
          title?: string;
          upc?: string;
          updated_at?: string | null;
          warranty?: string | null;
          weight_in_kg?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'product_category_1_fkey';
            columns: ['category_1'];
            isOneToOne: false;
            referencedRelation: 'category';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'product_category_2_fkey';
            columns: ['category_2'];
            isOneToOne: false;
            referencedRelation: 'category';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'product_main_category_fkey';
            columns: ['main_category'];
            isOneToOne: false;
            referencedRelation: 'category';
            referencedColumns: ['id'];
          },
        ];
      };
      product_review: {
        Row: {
          created_at: string | null;
          id: number;
          product_id: string;
          review: string;
          stars: number;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: number;
          product_id: string;
          review: string;
          stars: number;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: number;
          product_id?: string;
          review?: string;
          stars?: number;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'product_review_product_id_fkey';
            columns: ['product_id'];
            isOneToOne: false;
            referencedRelation: 'product';
            referencedColumns: ['csin'];
          },
        ];
      };
      today_circles: {
        Row: {
          circle_id: number | null;
          created_at: string | null;
          id: number;
        };
        Insert: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: number;
        };
        Update: {
          circle_id?: number | null;
          created_at?: string | null;
          id?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'today_circles_circle_id_fkey';
            columns: ['circle_id'];
            isOneToOne: false;
            referencedRelation: 'circle';
            referencedColumns: ['id'];
          },
        ];
      };
      user_profile: {
        Row: {
          address: Json | null;
          created_at: string | null;
          first_name: string | null;
          id: string;
          is_phone_verified: boolean | null;
          last_name: string | null;
          phone: string | null;
          preferences: Json | null;
          updated_at: string | null;
        };
        Insert: {
          address?: Json | null;
          created_at?: string | null;
          first_name?: string | null;
          id: string;
          is_phone_verified?: boolean | null;
          last_name?: string | null;
          phone?: string | null;
          preferences?: Json | null;
          updated_at?: string | null;
        };
        Update: {
          address?: Json | null;
          created_at?: string | null;
          first_name?: string | null;
          id?: string;
          is_phone_verified?: boolean | null;
          last_name?: string | null;
          phone?: string | null;
          preferences?: Json | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      vendor: {
        Row: {
          address: Json | null;
          approval_notes: string | null;
          approval_status:
            | Database['public']['Enums']['approval_status_enum']
            | null;
          approved_at: string | null;
          approved_by: string | null;
          contact_email: string;
          contact_phone: string | null;
          created_at: string | null;
          date_of_incorporation: string | null;
          description: string | null;
          headline: string | null;
          id: string;
          logo_url: string | null;
          name: string;
          slug: string;
          status: Database['public']['Enums']['account_status_enum'];
          updated_at: string | null;
          user_id: string | null;
          version: number | null;
        };
        Insert: {
          address?: Json | null;
          approval_notes?: string | null;
          approval_status?:
            | Database['public']['Enums']['approval_status_enum']
            | null;
          approved_at?: string | null;
          approved_by?: string | null;
          contact_email: string;
          contact_phone?: string | null;
          created_at?: string | null;
          date_of_incorporation?: string | null;
          description?: string | null;
          headline?: string | null;
          id: string;
          logo_url?: string | null;
          name: string;
          slug: string;
          status?: Database['public']['Enums']['account_status_enum'];
          updated_at?: string | null;
          user_id?: string | null;
          version?: number | null;
        };
        Update: {
          address?: Json | null;
          approval_notes?: string | null;
          approval_status?:
            | Database['public']['Enums']['approval_status_enum']
            | null;
          approved_at?: string | null;
          approved_by?: string | null;
          contact_email?: string;
          contact_phone?: string | null;
          created_at?: string | null;
          date_of_incorporation?: string | null;
          description?: string | null;
          headline?: string | null;
          id?: string;
          logo_url?: string | null;
          name?: string;
          slug?: string;
          status?: Database['public']['Enums']['account_status_enum'];
          updated_at?: string | null;
          user_id?: string | null;
          version?: number | null;
        };
        Relationships: [];
      };
      vendor_documents: {
        Row: {
          created_at: string | null;
          document_type: string;
          expires_at: string | null;
          id: string;
          is_verified: boolean | null;
          storage_object_id: string;
          updated_at: string | null;
          vendor_id: string;
          verification_notes: string | null;
          verified_at: string | null;
          verified_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          document_type: string;
          expires_at?: string | null;
          id?: string;
          is_verified?: boolean | null;
          storage_object_id: string;
          updated_at?: string | null;
          vendor_id: string;
          verification_notes?: string | null;
          verified_at?: string | null;
          verified_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          document_type?: string;
          expires_at?: string | null;
          id?: string;
          is_verified?: boolean | null;
          storage_object_id?: string;
          updated_at?: string | null;
          vendor_id?: string;
          verification_notes?: string | null;
          verified_at?: string | null;
          verified_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'vendor_documents_vendor_id_fkey';
            columns: ['vendor_id'];
            isOneToOne: false;
            referencedRelation: 'vendor';
            referencedColumns: ['id'];
          },
        ];
      };
      vendor_versions: {
        Row: {
          address: Json | null;
          approval_notes: string | null;
          approved_at: string | null;
          approved_by: string | null;
          contact_email: string;
          contact_phone: string | null;
          created_at: string | null;
          date_of_incorporation: string | null;
          description: string | null;
          headline: string | null;
          id: string;
          logo_url: string | null;
          name: string;
          rejected_at: string | null;
          rejected_by: string | null;
          rejection_reason: string | null;
          slug: string;
          status: string;
          submitted_at: string | null;
          submitted_by: string | null;
          updated_at: string | null;
          vendor_id: string;
          version_number: number;
        };
        Insert: {
          address?: Json | null;
          approval_notes?: string | null;
          approved_at?: string | null;
          approved_by?: string | null;
          contact_email: string;
          contact_phone?: string | null;
          created_at?: string | null;
          date_of_incorporation?: string | null;
          description?: string | null;
          headline?: string | null;
          id?: string;
          logo_url?: string | null;
          name: string;
          rejected_at?: string | null;
          rejected_by?: string | null;
          rejection_reason?: string | null;
          slug: string;
          status?: string;
          submitted_at?: string | null;
          submitted_by?: string | null;
          updated_at?: string | null;
          vendor_id: string;
          version_number: number;
        };
        Update: {
          address?: Json | null;
          approval_notes?: string | null;
          approved_at?: string | null;
          approved_by?: string | null;
          contact_email?: string;
          contact_phone?: string | null;
          created_at?: string | null;
          date_of_incorporation?: string | null;
          description?: string | null;
          headline?: string | null;
          id?: string;
          logo_url?: string | null;
          name?: string;
          rejected_at?: string | null;
          rejected_by?: string | null;
          rejection_reason?: string | null;
          slug?: string;
          status?: string;
          submitted_at?: string | null;
          submitted_by?: string | null;
          updated_at?: string | null;
          vendor_id?: string;
          version_number?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'vendor_versions_vendor_id_fkey';
            columns: ['vendor_id'];
            isOneToOne: false;
            referencedRelation: 'vendor';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      approve_vendor_version: {
        Args: { p_approval_notes?: string; p_version_id: string };
        Returns: boolean;
      };
      create_vendor_version: {
        Args: {
          p_address?: Json;
          p_contact_email: string;
          p_contact_phone?: string;
          p_date_of_incorporation?: string;
          p_description?: string;
          p_headline?: string;
          p_logo_url?: string;
          p_name: string;
          p_vendor_id: string;
        };
        Returns: string;
      };
      generate_product_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      generate_vendor_id: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      generate_vendor_slug: {
        Args: { vendor_name: string };
        Returns: string;
      };
      get_vendor_by_user_id: {
        Args: { user_uuid: string };
        Returns: {
          address: Json;
          approval_notes: string;
          approval_status: Database['public']['Enums']['approval_status_enum'];
          approved_at: string;
          approved_by: string;
          contact_email: string;
          contact_phone: string;
          created_at: string;
          date_of_incorporation: string;
          description: string;
          headline: string;
          id: string;
          logo_url: string;
          name: string;
          slug: string;
          status: Database['public']['Enums']['account_status_enum'];
          updated_at: string;
          version: number;
        }[];
      };
      get_vendor_versions: {
        Args: { p_vendor_id: string };
        Returns: {
          address: Json;
          approval_notes: string;
          approved_at: string;
          contact_email: string;
          contact_phone: string;
          created_at: string;
          date_of_incorporation: string;
          description: string;
          headline: string;
          id: string;
          logo_url: string;
          name: string;
          rejected_at: string;
          rejection_reason: string;
          slug: string;
          status: string;
          submitted_at: string;
          updated_at: string;
          version_number: number;
        }[];
      };
      is_approved_vendor: {
        Args: { user_uuid: string };
        Returns: boolean;
      };
      json_matches_schema: {
        Args: { instance: Json; schema: Json };
        Returns: boolean;
      };
      jsonb_matches_schema: {
        Args: { instance: Json; schema: Json };
        Returns: boolean;
      };
      jsonschema_is_valid: {
        Args: { schema: Json };
        Returns: boolean;
      };
      jsonschema_validation_errors: {
        Args: { instance: Json; schema: Json };
        Returns: string[];
      };
      reject_vendor_version: {
        Args: { p_rejection_reason: string; p_version_id: string };
        Returns: boolean;
      };
      submit_vendor_version_for_approval: {
        Args: { p_version_id: string };
        Returns: boolean;
      };
      validate_vendor_data: {
        Args: {
          vendor_email: string;
          vendor_name: string;
          vendor_phone?: string;
        };
        Returns: boolean;
      };
    };
    Enums: {
      account_status_enum: 'active' | 'inactive' | 'pending' | 'suspended';
      approval_status_enum: 'pending' | 'approved' | 'rejected' | 'suspended';
      circle_status_enum:
        | 'draft'
        | 'active'
        | 'closed'
        | 'cancelled'
        | 'completed';
      circle_type: 'fixed' | 'dynamic';
      product_status_enum: 'draft' | 'active' | 'inactive';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      account_status_enum: ['active', 'inactive', 'pending', 'suspended'],
      approval_status_enum: ['pending', 'approved', 'rejected', 'suspended'],
      circle_status_enum: [
        'draft',
        'active',
        'closed',
        'cancelled',
        'completed',
      ],
      circle_type: ['fixed', 'dynamic'],
      product_status_enum: ['draft', 'active', 'inactive'],
    },
  },
} as const;
