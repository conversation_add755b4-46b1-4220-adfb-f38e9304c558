'use client';
import {
  Button,
  Radio,
  RadioGroup,
  Text,
  VStack,
  Icon,
} from '@chakra-ui/react';
import { useEffect, useMemo, useState } from 'react';
import { FaCcVisa, FaCcMastercard, FaCreditCard } from 'react-icons/fa';

/**
 * A shared type definition for card details.
 */
type CardDetails = {
  name: string;
  number: string;
  month: string;
  year: string;
  cvc: string;
};

/**
 * A styled wrapper for a single selectable card option.
 */
const SavedCardOption = ({ children }: { children: React.ReactNode }) => (
  <VStack
    border="1px solid"
    borderColor="gray.200"
    borderRadius="md"
    p={4}
    w="full"
    align="start"
    cursor="pointer"
    _hover={{ bg: 'gray.50' }}
  >
    {children}
  </VStack>
);

/**
 * A simple utility to determine the card brand from its number.
 */
const getCardBrand = (cardNumber: string) => {
  if (cardNumber.startsWith('4')) {
    return { brand: 'Visa', icon: FaCcVisa };
  }
  if (cardNumber.startsWith('5')) {
    return { brand: 'Mastercard', icon: FaCcMastercard };
  }
  return { brand: 'Card', icon: FaCreditCard }; // Default fallback
};

/**
 * Defines the props for the main SavedCards component.
 */
type SavedCardsProps = {
  cards: CardDetails[];
  onAddNew: () => void;
  onSelect: (card: CardDetails | null) => void;
};

/**
 * A component that displays a list of saved cards passed via props.
 * This version is hardened against unstable parent props to prevent infinite loops.
 */
export const SavedCards = ({ cards, onAddNew, onSelect }: SavedCardsProps) => {
  const [selectedValue, setSelectedValue] = useState(cards[0]?.number || '');

  /**
   *  Stabilize the `cards` dependency to prevent infinite loops.
   */
  const stableCardsDependency = useMemo(() => JSON.stringify(cards), [cards]);

  useEffect(() => {
    const selectedCard = cards.find(card => card.number === selectedValue);
    onSelect(selectedCard || null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedValue, onSelect, stableCardsDependency]);

  return (
    <VStack align="stretch" spacing={4}>
      {cards.length > 0 ? (
        <RadioGroup onChange={setSelectedValue} value={selectedValue}>
          <VStack align="stretch">
            {cards.map(card => {
              const { brand, icon } = getCardBrand(card.number);
              const last4 = card.number.slice(-4);

              return (
                <SavedCardOption key={card.number}>
                  <Radio value={card.number} w="full">
                    <Icon
                      as={icon}
                      mr={2}
                      boxSize={6}
                      verticalAlign="middle"
                      color="blue.500"
                    />
                    <Text as="span" fontWeight="medium">
                      {brand} ending in {last4}
                    </Text>
                  </Radio>
                </SavedCardOption>
              );
            })}
          </VStack>
        </RadioGroup>
      ) : (
        <VStack textAlign="center" p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" color="gray.600">
            You have no saved cards.
          </Text>
        </VStack>
      )}

      <Button variant="outline" onClick={onAddNew}>
        Use another card
      </Button>
    </VStack>
  );
};
