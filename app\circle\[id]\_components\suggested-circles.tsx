'use client';
import { Box, Heading, SimpleGrid } from '@chakra-ui/react';
import { CircleCard } from '@/components/cards';
import { Circle } from '@/utils/supabase/queries';

interface RelatedCirclesProps {
  relatedCircles: Circle[];
}

const RelatedCircles = ({ relatedCircles }: RelatedCirclesProps) => {
  if (!relatedCircles || relatedCircles.length === 0) return null;
  return (
    <Box
      as="section"
      py={8}
      px={{ base: 4, md: 8 }}
      maxW="container.xl"
      mx="auto"
    >
      <Heading as="h2" size="lg" mb={6}>
        Other Circles
      </Heading>

      <SimpleGrid
        columns={{ base: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
        spacing={{ base: 3, md: 5 }}
      >
        {relatedCircles?.map(circle => (
          <CircleCard key={circle.id} circle={circle} />
        ))}
      </SimpleGrid>
    </Box>
  );
};

export default RelatedCircles;
