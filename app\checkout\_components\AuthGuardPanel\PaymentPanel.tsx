'use client';
import { Heading, VStack, useToast } from '@chakra-ui/react';
import { User } from '@supabase/supabase-js';
import { useState } from 'react';
import { useCart } from '@/providers/CartContext';
import { MoyasarCustomForm } from './MoyasarCustomForm';
import { Actions } from './Actions';

type CardDetails = {
  name: string;
  number: string;
  month: string;
  year: string;
  cvc: string;
};

type PaymentPanelProps = {
  user: User | null;
};

export const PaymentPanel = ({ user }: PaymentPanelProps) => {
  const { cartId } = useCart();
  const toast = useToast();

  console.log('User:', user);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string[]>>({});

  const [newCardDetails, setNewCardDetails] = useState<CardDetails>({
    name: '',
    number: '',
    month: '',
    year: '',
    cvc: '',
  });

  const MOYASAR_KEY = process.env.NEXT_PUBLIC_MOYASAR_PUBLISHABLE_KEY as string;

  const processPayment = async (details: CardDetails) => {
    try {
      const tokenResponse = await fetch('https://api.moyasar.com/v1/tokens', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          publishable_api_key: MOYASAR_KEY,
          save_only: true,
          ...details,
        }),
      });
      const tokenData = await tokenResponse.json();
      if (!tokenResponse.ok) throw tokenData;

      const token = tokenData.id;
      console.log(cartId);
      const paymentResponse = await fetch('/api/payment/initiate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token, cartId }),
      });
      const paymentData = await paymentResponse.json();
      if (!paymentResponse.ok) throw paymentData;

      if (paymentData.transaction_url) {
        window.location.href = paymentData.transaction_url;
      } else {
        throw new Error('Transaction URL not received.');
      }
    } catch (error) {
      console.error('Payment process error:', error);
      toast({
        title: 'Payment Failed',
        description: `${error}` || 'Data validation failed.',
        status: 'error',
      });
      throw error;
    }
  };

  const handlePlaceOrder = async () => {
    if (!cartId) {
      toast({
        title: 'Cart session not found.',
        description: 'Please try adding the item again.',
        status: 'error',
      });
      return;
    }

    setIsLoading(true);
    setErrors({});
    try {
      await processPayment(newCardDetails);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <VStack bg="white" p={6} borderRadius="xl" align="stretch" spacing={6}>
      <Heading as="h2" size="md" color="gray.800">
        Payment
      </Heading>
      <MoyasarCustomForm
        cardDetails={newCardDetails}
        setCardDetails={setNewCardDetails}
        errors={errors}
      />
      <Actions onPlaceOrder={handlePlaceOrder} isLoading={isLoading} />
    </VStack>
  );
};
