import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const payment = await request.json();

  // Basic validation
  if (!payment || !payment.id || !payment.status) {
    return NextResponse.json(
      { error: 'Invalid payment data provided.' },
      { status: 400 }
    );
  }

  try {
    // Here, you would save the payment to your own `payments` or `orders` table.
    // This is a critical step for reconciliation.
    /*
     * const { error } = await supabase.from('payments').insert({
      id: payment.id, // Moyasar's payment ID
      status: payment.status,
      amount: payment.amount,
      currency: payment.currency,
      description: payment.description,
      user_id: user?.id, // Associate with user if they are logged in
      metadata: payment, // Store the full payment object for reference
    });
    

    if (error) {
      throw new Error(`Supabase error: ${error.message}`);
    }
    */

    return NextResponse.json({ success: true, saved_payment_id: payment.id });
  } catch (error) {
    console.error('Failed to save payment on backend:', error);
    return NextResponse.json(
      { success: false, error: `${error}` || 'Failed to save payment.' },
      { status: 500 }
    );
  }
}
