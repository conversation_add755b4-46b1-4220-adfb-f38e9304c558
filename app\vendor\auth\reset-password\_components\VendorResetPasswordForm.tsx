'use client';

import { useActionState, useRef, useEffect } from 'react';
import {
  vendorResetPassword,
  type VendorResetPasswordState,
} from '@/app/actions/vendor-auth';
import NextLink from 'next/link';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  VStack,
  Text,
  Link as ChakraLink,
  Alert,
  AlertIcon,
  AlertTitle,
  Spinner,
} from '@chakra-ui/react';

export default function VendorResetPasswordForm() {
  const formRef = useRef<HTMLFormElement>(null);

  const [formState, formAction, isPending] = useActionState<
    VendorResetPasswordState,
    FormData
  >(vendorResetPassword, { error: null, success: false, message: null });

  useEffect(() => {
    if (formState.error) {
      formRef.current?.reset();
    }
  }, [formState]);

  return (
    <Flex
      minH="100vh"
      align="start"
      justify="center"
      bg="gray.50"
      px={4}
      mt={10}
    >
      <Box
        maxW="md"
        w="full"
        bg="white"
        borderRadius="2xl"
        boxShadow="lg"
        p={8}
      >
        <VStack spacing={6} align="stretch">
          <Heading
            as="h1"
            size="lg"
            textAlign="center"
            color="gray.800"
            fontWeight="bold"
          >
            Set New Password
          </Heading>

          <Text textAlign="center" color="gray.600" fontSize="sm">
            Enter your new password below
          </Text>

          {formState.error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {formState.error}
            </Alert>
          )}

          {formState.success && formState.message && (
            <Alert status="success" borderRadius="md">
              <AlertIcon />
              <AlertTitle>{formState.message}</AlertTitle>
            </Alert>
          )}

          <form ref={formRef} action={formAction}>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel htmlFor="password" color="gray.600">
                  New Password
                </FormLabel>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  focusBorderColor="primary"
                  placeholder="8+ characters password"
                  disabled={isPending}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel htmlFor="confirmPassword" color="gray.600">
                  Confirm New Password
                </FormLabel>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  focusBorderColor="primary"
                  placeholder="Repeat your password"
                  disabled={isPending}
                />
              </FormControl>

              <Button
                type="submit"
                bg="#AFFF02"
                color="black"
                size="lg"
                w="full"
                fontWeight="bold"
                isLoading={isPending}
                spinner={<Spinner size="sm" color="black" />}
                _hover={{
                  bg: '#99e602',
                }}
                disabled={isPending}
              >
                Update Password
              </Button>
            </VStack>
          </form>

          <Text textAlign="center">
            <ChakraLink
              as={NextLink}
              href="/vendor/auth/login"
              color="blue.600"
              fontWeight="medium"
              _hover={{ color: 'blue.500' }}
            >
              Back to Sign In
            </ChakraLink>
          </Text>
        </VStack>
      </Box>
    </Flex>
  );
}
