### Notes for development

#### Checkout

#### Security Notes

In `api/initiate-payment/route.ts` we are accepting amount from client via an http request
this can result in a scenarios where users are able to tamper with final amount to pay by just changing the amount in the request body ... We should not allow this.

**Solution** : Create a cart table where we save user's cart data, so we can fetch it later
to check and use the total amount (amount to pay).

Same goes for `MoyasaDropInForm.tsx` where we accept amount via prop (but use cart context) .. amount should be fetched from `cart` table (supabase) and not context (local storage).

For now for demo purpose we allow this but we should address this later.
