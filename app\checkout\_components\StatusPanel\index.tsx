'use client';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import { <PERSON>lex, Spinner, VStack, Text } from '@chakra-ui/react';
import { finalizePaymentAction } from '@/app/actions/payment';
import { SuccessDisplay } from './SuccessDisplay';
import { FailureDisplay } from './FailureDisplay';

type VerificationStatus = 'verifying' | 'success' | 'failed';

type StatusPanelProps = {
  onBack: () => void;
};

export const StatusPanel = ({ onBack }: StatusPanelProps) => {
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<VerificationStatus>('verifying');
  const [message, setMessage] = useState('Securely finalizing your payment...');
  const [hasVerified, setHasVerified] = useState(false);

  const finalize = useCallback(async (paymentId: string) => {
    const result = await finalizePaymentAction(paymentId);

    setStatus(result.status === 'success' ? 'success' : 'failed');
    if (result.status !== 'success') {
      setMessage(result.message || 'An unknown error occurred.');
    }
  }, []);

  useEffect(() => {
    const paymentId = searchParams.get('id');
    if (paymentId && !hasVerified) {
      setHasVerified(true);
      finalize(paymentId);
    } else if (!paymentId && !hasVerified) {
      const paramsReady = Array.from(searchParams.entries()).length > 0;
      if (paramsReady) {
        setMessage('No payment ID found in the redirect.');
        setStatus('failed');
      }
    }
  }, [searchParams, finalize, hasVerified]);

  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return (
          <VStack spacing={4}>
            <Spinner size="xl" />
            <Text color="gray.500">{message}</Text>
          </VStack>
        );
      case 'success':
        return <SuccessDisplay />;
      case 'failed':
        return <FailureDisplay message={message} onTryAgain={onBack} />;
      default:
        return null;
    }
  };

  return (
    <Flex align="center" justify="center" minH="80vh" bg="gray.100" p={4}>
      {renderContent()}
    </Flex>
  );
};
