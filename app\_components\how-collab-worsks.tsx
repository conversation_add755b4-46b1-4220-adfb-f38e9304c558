import { Container, Heading, Text, SimpleGrid, VStack } from '@chakra-ui/react';
import { HowItWorksCard } from '@/components/cards';

const stepsData = [
  {
    imageSrc: '/images/browse-product.png',
    title: 'Browse a Product',
    description: 'Find hundreds of deals across 12 categories.',
  },
  {
    imageSrc: '/images/earlier-join.png',
    title: (
      <>
        Join a Circle & Pay
        <br />
        Your Tier Price
      </>
    ),
    description: 'The earlier you join, the lower your price.',
  },
  {
    imageSrc: '/images/when-enough.png',
    title: (
      <>
        Circle Closes When
        <br />
        Target is Met
      </>
    ),
    description: 'When enough shoppers join, the deal is locked in.',
  },
  {
    imageSrc: '/images/everyone-saves.png',
    title: (
      <>
        We Ship,
        <br />
        Everyone Saves
      </>
    ),
    description: 'Bulk ordering means lower costs for the whole circle.',
  },
];

export const HowCollabWorks = () => {
  return (
    <Container maxW="container.xl" py={{ base: '10', md: '12' }}>
      <VStack spacing={2} alignItems="start" mb={{ base: 10, md: 14 }}>
        <Heading as="h2" size={{ base: 'md', md: 'lg' }}>
          How Collab Works
        </Heading>
        <Text fontSize={{ base: 'sm', md: 'md' }} color="blackAlpha.700">
          Group buying made simple in four steps.
        </Text>
      </VStack>

      <SimpleGrid
        columns={{ base: 1, sm: 2, lg: 4 }}
        spacing={{ base: 6, md: 8 }}
      >
        {stepsData.map((step, index) => (
          <HowItWorksCard
            key={index}
            imageSrc={step.imageSrc}
            title={step.title}
            description={step.description}
          />
        ))}
      </SimpleGrid>
    </Container>
  );
};

export default HowCollabWorks;
