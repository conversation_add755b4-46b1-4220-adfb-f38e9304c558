# Collab Docs

Welcome to the **Collab developer documentation** 👋  
This is the central hub for all technical notes, developer guides, and project knowledge related to the **Collab group buying web app**.

---

## 📖 About Collab

Collab is a modern **group buying platform** built with the goal of making collective purchasing simple, transparent, and rewarding.  
It empowers communities and friends to pool resources together for better deals.

---

## 🚀 Tech Stack

- **Framework**: [Next.js](https://nextjs.org/) — React-based, full-stack framework for fast and scalable apps.
- **UI Library**: [Chakra UI v2](https://chakra-ui.com/) — Accessible and themeable component library.
- **Backend & Auth**: [Supabase](https://supabase.com/) — Postgres, auth, and storage as a service.
- **Docs**: Markdown-based, stored in `/docs`.

---

## 📂 Documentation Structure

This documentation will grow with the project. For now, it includes:

- **Getting Started** → How to set up the dev environment.
- **Architecture** → Overview of project structure and design decisions.
- **UI Guidelines** → Chakra UI usage, theme setup, and components.
- **Database** → Supabase schema, migrations, and queries.
- **API** → Routes, endpoints, and server functions.
- **Conventions** → Naming, commit rules, and coding standards.
- **Changelog** → Notes on major updates and migrations.

---

## 🛠 Getting Started (Quick Glance)

#### Clone repository

```bash
git https://github.com/gehadshaat/collab-customer-web.git
cd collab-customer-web
git checkout dev
```

#### Install dependencies

```bash
npm install
```

#### Run development server

```bash
npm run dev
```

#### Open in browser

http://localhost:3000
