-------------------------------------------------
-- Initial Schema Migration
-- Based on db/schema.sql
-------------------------------------------------

-- 1) Create Extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_jsonschema";

-- 2) Define Enum Types
DO $$ BEGIN
    CREATE TYPE public.account_status_enum AS ENUM ('active', 'inactive', 'pending', 'suspended');
    CREATE TYPE public.product_status_enum AS ENUM ('draft', 'active', 'inactive');
    CREATE TYPE public.circle_status_enum AS ENUM ('draft', 'active', 'closed', 'cancelled', 'completed');
    CREATE TYPE public.circle_type AS ENUM ('fixed', 'dynamic');
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- 3) Create vendor table
CREATE TABLE IF NOT EXISTS public.vendor (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    logo_url TEXT,
    contact_email TEXT NOT NULL,
    contact_phone TEXT,
    address JSONB,
    status public.account_status_enum NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_email CHECK (contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- 4) Create category table
CREATE TABLE IF NOT EXISTS public.category (
    id TEXT PRIMARY KEY,
    slug TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    category_image TEXT,
    parent TEXT REFERENCES public.category(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 5) Create product table
CREATE TABLE IF NOT EXISTS public.product (
    csin TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    brand TEXT NOT NULL,
    model TEXT NOT NULL,
    sku TEXT NOT NULL,
    upc TEXT NOT NULL,
    market_price NUMERIC(15,2) NOT NULL,
    currency TEXT DEFAULT 'SAR',
    weight_in_kg NUMERIC(15,2) NOT NULL,
    dimension_height_in_cm NUMERIC(15,2) NOT NULL,
    dimension_width_in_cm NUMERIC(15,2) NOT NULL,
    dimension_length_in_cm NUMERIC(15,2) NOT NULL,
    country_of_origin TEXT NOT NULL,
    warranty TEXT,
    slug TEXT NOT NULL UNIQUE,
    meta_title TEXT,
    meta_description TEXT,
    meta_keywords TEXT[],
    search_vector TSVECTOR,
    status public.product_status_enum NOT NULL DEFAULT 'draft',
    about TEXT,
    product_image TEXT,
    main_category TEXT REFERENCES public.category(id),
    category_1 TEXT REFERENCES public.category(id),
    category_2 TEXT REFERENCES public.category(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 6) Create circle table
CREATE TABLE IF NOT EXISTS public.circle (
    id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    status public.circle_status_enum NOT NULL DEFAULT 'draft',
    description TEXT,
    vendor_id TEXT REFERENCES public.vendor(id),
    product_id TEXT REFERENCES public.product(csin),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    sale_price NUMERIC(15,2) NOT NULL,
    pricing_tiers JSONB,
    max_participants INTEGER NOT NULL,
    min_quantity INTEGER NOT NULL DEFAULT 1,
    max_quantity INTEGER NOT NULL DEFAULT 1,
    current_participants INTEGER DEFAULT 0,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    circle_type public.circle_type DEFAULT 'fixed',
    CONSTRAINT valid_dates CHECK (end_date > start_date),
    CONSTRAINT valid_current CHECK (current_participants <= max_participants)
);

-- 7) Create user_profile table
CREATE TABLE IF NOT EXISTS public.user_profile (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    is_phone_verified BOOLEAN DEFAULT FALSE,
    address JSONB,
    preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 8) Create cart table
CREATE TABLE IF NOT EXISTS public.cart (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    circle_id BIGINT REFERENCES public.circle(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_quantity CHECK (quantity > 0)
);

-- 9) Create order table
CREATE TABLE IF NOT EXISTS public.order (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    circle_id BIGINT REFERENCES public.circle(id),
    quantity INTEGER NOT NULL,
    unit_price NUMERIC(15,2) NOT NULL,
    total_price NUMERIC(15,2) NOT NULL,
    status TEXT DEFAULT 'pending',
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    shipping_address JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 10) Create flash_circles table
CREATE TABLE IF NOT EXISTS public.flash_circles (
    id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    circle_id BIGINT REFERENCES public.circle(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 11) Create today_circles table
CREATE TABLE IF NOT EXISTS public.today_circles (
    id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    circle_id BIGINT REFERENCES public.circle(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 12) Create product_review table
CREATE TABLE IF NOT EXISTS public.product_review (
    id BIGINT PRIMARY KEY GENERATED BY DEFAULT AS IDENTITY,
    product_id TEXT NOT NULL REFERENCES public.product(csin),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    stars SMALLINT NOT NULL CHECK (stars >= 1 AND stars <= 5),
    review TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 13) Create functions
CREATE OR REPLACE FUNCTION public.generate_product_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'CSIN' || LPAD(nextval('product_id_seq')::TEXT, 8, '0');
END;
$$ LANGUAGE plpgsql;

-- Create sequence for product IDs
CREATE SEQUENCE IF NOT EXISTS product_id_seq START 1;

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profile (id)
    VALUES (NEW.id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function for product search
CREATE OR REPLACE FUNCTION public.product_search_trigger()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english',
        COALESCE(NEW.name, '') || ' ' ||
        COALESCE(NEW.title, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.brand, '') || ' ' ||
        COALESCE(NEW.model, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 11) Create triggers
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

CREATE TRIGGER product_search_update
    BEFORE INSERT OR UPDATE ON public.product
    FOR EACH ROW
    EXECUTE FUNCTION public.product_search_trigger();

CREATE TRIGGER set_updated_at_vendor
    BEFORE UPDATE ON public.vendor
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER set_updated_at_product
    BEFORE UPDATE ON public.product
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER set_updated_at_circle
    BEFORE UPDATE ON public.circle
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 12) Enable Row Level Security
ALTER TABLE public.vendor ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.circle ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profile ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.category ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flash_circles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.today_circles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_review ENABLE ROW LEVEL SECURITY;

-- 13) Create RLS Policies
-- Public read access for products
CREATE POLICY "Public products are visible to everyone"
    ON public.product FOR SELECT
    TO public
    USING (true);

-- Public read access for circles
CREATE POLICY "Circles are visible to everyone"
    ON public.circle FOR SELECT
    TO public
    USING (true);

-- Public read access for vendors
CREATE POLICY "Vendors are visible to everyone"
    ON public.vendor FOR SELECT
    TO public
    USING (true);

-- Public read access for categories
CREATE POLICY "Categories are visible to everyone"
    ON public.category FOR SELECT
    TO public
    USING (true);

-- User profile policies
CREATE POLICY "Users can view their own profile"
    ON public.user_profile FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
    ON public.user_profile FOR UPDATE
    USING (auth.uid() = id);

-- Cart policies
CREATE POLICY "Users can view their own cart"
    ON public.cart FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own cart"
    ON public.cart FOR ALL
    USING (auth.uid() = user_id);

-- Order policies
CREATE POLICY "Users can view their own orders"
    ON public.order FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own orders"
    ON public.order FOR INSERT
    WITH CHECK (auth.uid() = user_id);

-- Public read access for flash_circles
CREATE POLICY "Flash circles are visible to everyone"
    ON public.flash_circles FOR SELECT
    TO public
    USING (true);

-- Public read access for today_circles
CREATE POLICY "Today circles are visible to everyone"
    ON public.today_circles FOR SELECT
    TO public
    USING (true);

-- Product review policies
CREATE POLICY "Product reviews are visible to everyone"
    ON public.product_review FOR SELECT
    TO public
    USING (true);

CREATE POLICY "Users can create their own product reviews"
    ON public.product_review FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own product reviews"
    ON public.product_review FOR UPDATE
    USING (auth.uid() = user_id);

-- 14) Create indexes
CREATE INDEX idx_product_search ON public.product USING GIN (search_vector);
CREATE INDEX idx_circle_dates ON public.circle (start_date, end_date);
CREATE INDEX idx_circle_status ON public.circle (status);
CREATE INDEX idx_product_status ON public.product (status);
CREATE INDEX idx_vendor_status ON public.vendor (status);
CREATE INDEX idx_category_parent ON public.category (parent);
CREATE INDEX idx_user_profile_id ON public.user_profile (id);
CREATE INDEX idx_flash_circles_circle_id ON public.flash_circles (circle_id);
CREATE INDEX idx_today_circles_circle_id ON public.today_circles (circle_id);
CREATE INDEX idx_product_review_product_id ON public.product_review (product_id);
CREATE INDEX idx_product_review_user_id ON public.product_review (user_id);
CREATE INDEX idx_product_review_stars ON public.product_review (stars);

-- 15) Create storage buckets (if not exists)
INSERT INTO storage.buckets (id, name, public)
VALUES
    ('products-images', 'products-images', true),
    ('category-image', 'category-image', true),
    ('vendor-logos', 'vendor-logos', true)
ON CONFLICT (id) DO NOTHING;