import { type NextRequest } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';

export async function middleware(request: NextRequest) {
  return await updateSession(request);
}

// Only run middleware on API routes
export const config = {
  matcher: [
    '/api/:path((?!circles|products|verify|payment).*)', // all routes are behind auth except /api/circles and /api/products
  ],
};
