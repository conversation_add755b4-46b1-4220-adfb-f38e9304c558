'use server';

import { createClient } from '@/utils/supabase/server';
import type { ProfileFormData } from '@/types/database';

export async function updateProfile(formData: FormData) {
  const supabase = await createClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();
  if (!session) {
    return { error: 'Not authenticated' };
  }

  const updates: ProfileFormData = {
    first_name: formData.get('first_name') as string,
    last_name: formData.get('last_name') as string,
    phone: formData.get('phone') as string,
    address: {
      street: formData.get('street') as string,
      city: formData.get('city') as string,
      state: formData.get('state') as string,
      postal_code: formData.get('postal_code') as string,
      country: formData.get('country') as string,
    },
  };

  try {
    const { error } = await supabase
      .from('user_profile')
      .update(updates)
      .eq('id', session.user.id);

    if (error) throw error;

    return { success: true };
  } catch (err) {
    console.error('Error updating profile:', err);
    return { error: 'Failed to update profile' };
  }
}
