'use server';

import { createClient } from '@/utils/supabase/server';
import { MailerSend, EmailPara<PERSON>, Sender, Recipient } from 'mailersend';

console.log('MailerSend API Token exists:', !!process.env.MAILERSEND_API_KEY);
console.log('Token length:', process.env.MAILERSEND_API_KEY?.length);
const mailerSend = new MailerSend({
  apiKey: process.env.MAILERSEND_API_KEY || '',
});

type FinalizationResult = {
  status: 'success' | 'failed' | 'error';
  message?: string;
};

export async function finalizePaymentAction(
  paymentId: string
): Promise<FinalizationResult> {
  const secretKey = process.env.MOYASAR_SECRET_KEY;
  const supabase = await createClient();

  if (!paymentId) return { status: 'error', message: 'Payment ID is missing.' };
  if (!secretKey)
    return {
      status: 'error',
      message: 'Server is not configured for payments.',
    };

  try {
    // --- Step 1: Securely FETCH the payment from Moyasar to get its status and metadata ---
    const verifyResponse = await fetch(
      `https://api.moyasar.com/v1/payments/${paymentId}`,
      {
        headers: {
          Authorization: `Basic ${Buffer.from(`${secretKey}:`).toString('base64')}`,
        },
      }
    );
    const payment = await verifyResponse.json();
    if (!verifyResponse.ok) {
      throw new Error(
        payment.message || 'Failed to fetch payment details from Moyasar.'
      );
    }

    // Extract our cart ID from the trusted metadata ---
    const cartId = payment.metadata?.cart_id;
    if (!cartId) {
      throw new Error(
        `Critical Error: cart_id not found in metadata for payment ${paymentId}`
      );
    }

    // --- Step 2: Now, securely fetch OUR cart using the trusted cartId ---
    const { data: cart, error: cartError } = await supabase
      .from('cart')
      .select('*, user_email:user_id(email)')
      .eq('id', cartId)
      .single();

    if (cartError || !cart) {
      throw new Error(`Could not find a matching cart for cart_id: ${cartId}`);
    }

    let finalStatus: 'paid' | 'captured' | null = null;

    // --- Step 3: DECIDE and CAPTURE based on the status ---
    switch (payment.status) {
      case 'paid':
        finalStatus = 'paid';
        break;
      case 'authorized':
        const captureResponse = await fetch(
          `https://api.moyasar.com/v1/payments/${paymentId}/capture`,
          {
            method: 'POST',
            headers: {
              Authorization: `Basic ${Buffer.from(`${secretKey}:`).toString('base64')}`,
            },
          }
        );
        const captureData = await captureResponse.json();
        if (!captureResponse.ok || captureData.status !== 'captured') {
          throw new Error(
            captureData.message || 'Failed to capture the authorized payment.'
          );
        }
        finalStatus = 'captured';
        break;
      default:
        return {
          status: 'failed',
          message: payment.source?.message || 'Payment was not successful.',
        };
    }

    // --- Step 4: Finalize Business Logic (Create Order & Send Email) ---
    if (finalStatus) {
      const customerEmail = cart.user_email;
      console.log(customerEmail);
      if (!customerEmail) {
        throw new Error(`Customer email not found for cart ${cart.id}.`);
      }

      // Create order record with correct schema fields
      const { error: orderError } = await supabase.from('order').insert({
        user_id: cart.user_id,
        circle_id: cart.circle_id,
        quantity: cart.quantity,
        unit_price: payment.amount / cart.quantity, // Calculate unit price
        total_price: payment.amount,
        status: 'pending',
        payment_method: payment.source?.type || 'unknown',
        payment_status: payment.status,
      });
      if (orderError)
        throw new Error(`Failed to save order: ${orderError.message}`);

      const sentFrom = new Sender(
        '<EMAIL>',
        'Collab'
      );

      const recipients = [new Recipient(customerEmail, 'Valued Customer')];
      const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-SA', {
          style: 'currency',
          currency: 'SAR',
        }).format(amount);
      };
      console.log('Formatted amount:', formatCurrency(payment.amount / 100));
      const htmlContent = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
  <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
    <tr>
      <td align="center" style="padding: 25px 20px; background-color: #4a6cf7; border-radius: 8px 8px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 24px;">Order Confirmed!</h1>
      </td>
    </tr>
    <tr>
      <td style="padding: 25px 20px;">
        <p style="margin: 0 0 15px 0; color: #333; line-height: 1.5;">Hi there,</p>
        <p style="margin: 0 0 20px 0; color: #333; line-height: 1.5;">Thank you for your order! Your payment of <strong>${payment.amount_format}</strong> was successfully processed.</p>
        
        <div style="background-color: #f8f9fa; border-radius: 6px; padding: 15px; margin: 20px 0;">
          <h3 style="margin: 0 0 15px 0; color: #2d3748; font-size: 18px;">Order Summary</h3>
        </div>
        
        <p style="margin: 20px 0 0 0; color: #333; line-height: 1.5; font-size: 14px;">
          We'll notify you when your order ships. If you have any questions, please contact our support team.
        </p>
      </td>
    </tr>
    <tr>
      <td style="padding: 20px; text-align: center; background-color: #f1f5f9; border-radius: 0 0 8px 8px;">
        <p style="margin: 0; color: #718096; font-size: 13px;">&copy; 2025 Collab. All rights reserved.</p>
      </td>
    </tr>
  </table>
</div>
`;

      const emailParams = new EmailParams()
        .setFrom(sentFrom)
        .setTo(recipients)
        .setSubject('Your Order is Confirmed!')
        .setHtml(htmlContent);
      //  .setText(textContent); // Keep your text version for better deliverability

      await mailerSend.email.send(emailParams);

      return { status: 'success' };
    }

    return { status: 'failed', message: 'An unknown payment status occurred.' };
  } catch (error) {
    console.error('Finalize Payment Action Error:', error);
    return { status: 'error', message: `${error}` };
  }
}
