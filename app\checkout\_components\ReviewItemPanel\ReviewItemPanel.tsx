'use client';

import {
  Box,
  Grid,
  GridItem,
  Heading,
  VStack,
  Text,
  Flex,
} from '@chakra-ui/react';
import { useCart } from '@/providers/CartContext';
import { CartItemsCard } from './CartItemsCard';
import { CartSummaryCard } from './CartSummaryCard';

type ReviewItemPanelProps = {
  onNext: () => void;
};

const ReviewItemPanel = ({ onNext }: ReviewItemPanelProps) => {
  const { item, cartTotal } = useCart();

  if (!item) {
    return (
      <Flex align="center" justify="center" minH="60vh">
        <VStack>
          <Heading>Your Cart is Empty</Heading>
          <Text>Looks like you have not added anything to your cart yet.</Text>
        </VStack>
      </Flex>
    );
  }

  const itemsArray = [item];

  const summaryData = {
    subtotal: cartTotal,
    shippingFee: 'Free' as const,
    total: cartTotal,
    nextTierPrice: item.sale_price * (item.quantity + 1),
    nextTierJoiners: 1,
    priceHoldUntil: '04:58',
    progressValue: Math.min(100, (item.quantity / 5) * 100),
  };

  return (
    <Box minH="100vh" p={{ base: 4, md: 8 }} mt={10}>
      <Grid
        templateColumns={{ base: '1fr', lg: '2fr 1.2fr' }}
        gap={{ base: 6, lg: 8 }}
        maxW="container.xl"
        mx="auto"
      >
        <GridItem>
          <VStack align="stretch" spacing={6}>
            <Heading as="h1" size="lg" fontWeight="bold">
              Checkout
            </Heading>
            <CartItemsCard items={itemsArray} />
          </VStack>
        </GridItem>

        <GridItem>
          <CartSummaryCard summary={summaryData} handleNext={onNext} />
        </GridItem>
      </Grid>
    </Box>
  );
};

export default ReviewItemPanel;
