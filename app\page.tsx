import {
  TodayCircles,
  EndsSoonCircles,
  SuggestedCircles,
  ShopByCategory,
  HowCollabWorks,
  UserTrust,
  HeroSection,
} from './_components';
import { getEndSoonCircles, getTodayCircles } from '@/utils/supabase/queries';
export default async function Home() {
  const todayCircles = await getTodayCircles();
  const { data: endSoonCircles } = await getEndSoonCircles();
  return (
    <div className="min-h-screen">
      <main className="mx-auto my-5">
        <HeroSection />
        <TodayCircles circles={todayCircles} />
        <EndsSoonCircles circles={endSoonCircles} />
        <SuggestedCircles circles={endSoonCircles} />
        <ShopByCategory />
        <HowCollabWorks />
        <UserTrust />
      </main>
    </div>
  );
}
