import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import VendorResetPasswordForm from './_components/VendorResetPasswordForm';

export default async function VendorResetPasswordPage() {
  const supabase = await createClient();

  // Check if user is already logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect('/vendor/dashboard');
  }

  return <VendorResetPasswordForm />;
}
